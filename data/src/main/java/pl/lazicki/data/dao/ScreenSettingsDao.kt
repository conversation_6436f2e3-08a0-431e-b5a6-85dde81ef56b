package pl.lazicki.data.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import pl.lazicki.data.entity.ScreenSettingsEntity
import javax.inject.Singleton

@Dao
interface ScreenSettingsDao {
    
    @Query("SELECT * FROM screen_settings WHERE id = 1")
    fun observeScreenSettings(): Flow<ScreenSettingsEntity?>
    
    @Query("SELECT * FROM screen_settings WHERE id = 1")
    fun getScreenSettings(): ScreenSettingsEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScreenSettings(screenSettings: ScreenSettingsEntity)
    
    @Update
    suspend fun updateScreenSettings(screenSettings: ScreenSettingsEntity)
    
    @Delete
    suspend fun deleteScreenSettings(screenSettings: ScreenSettingsEntity)
    
    @Query("DELETE FROM screen_settings")
    suspend fun deleteAllScreenSettings()
}
