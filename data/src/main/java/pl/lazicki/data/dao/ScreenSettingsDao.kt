package pl.lazicki.data.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import pl.lazicki.data.entity.ScreenSettingsEntity

@Dao
interface ScreenSettingsDao {
    
    @Query("SELECT * FROM screen_settings WHERE id = 1")
    fun getScreenSettings(): Flow<ScreenSettingsEntity?>
    
    @Query("SELECT * FROM screen_settings WHERE id = 1")
    suspend fun getScreenSettingsOnce(): ScreenSettingsEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScreenSettings(screenSettings: ScreenSettingsEntity)
    
    @Update
    suspend fun updateScreenSettings(screenSettings: ScreenSettingsEntity)
    
    @Delete
    suspend fun deleteScreenSettings(screenSettings: ScreenSettingsEntity)
    
    @Query("DELETE FROM screen_settings")
    suspend fun deleteAllScreenSettings()
}
