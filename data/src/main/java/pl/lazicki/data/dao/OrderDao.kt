package pl.lazicki.data.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState
import javax.inject.Singleton

@Dao
interface OrderDao {

    @Query("SELECT number FROM orders ORDER BY id DESC LIMIT 1")
    fun observeLastOrderNumber(): Flow<Int>

    @Query("SELECT * FROM orders")
    fun observeAllOrders(): Flow<List<OrderEntity>>

    @Query("SELECT * FROM orders")
    fun getAllOrders(): List<OrderEntity>
    
    @Query("SELECT * FROM orders WHERE id = :id")
    suspend fun getOrderById(id: Long): OrderEntity?
    
    @Query("SELECT * FROM orders WHERE number = :number")
    suspend fun getOrderByNumber(number: Int): OrderEntity?
    
    @Query("SELECT * FROM orders WHERE state = :state")
    fun getOrdersByState(state: OrderState): Flow<List<OrderEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrder(order: OrderEntity): Long
    
    @Insert
    suspend fun insertOrders(orders: List<OrderEntity>)
    
    @Update
    suspend fun updateOrder(order: OrderEntity)
    
    @Delete
    suspend fun deleteOrder(order: OrderEntity)
    
    @Query("DELETE FROM orders WHERE id = :id")
    suspend fun deleteOrderById(id: Long)
    
    @Query("DELETE FROM orders")
    suspend fun deleteAllOrders()

    @Query("SELECT * FROM orders WHERE state = :state")
    suspend fun getAllCollectedOrders(state: OrderState = OrderState.Deleted): List<OrderEntity>
}
