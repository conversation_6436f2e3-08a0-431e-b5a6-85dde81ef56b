package pl.lazicki.data.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import pl.lazicki.data.entity.OrderEntity

@Dao
interface OrderDao {
    
    @Query("SELECT * FROM orders")
    fun getAllOrders(): Flow<List<OrderEntity>>
    
    @Query("SELECT * FROM orders WHERE id = :id")
    suspend fun getOrderById(id: Long): OrderEntity?
    
    @Query("SELECT * FROM orders WHERE number = :number")
    suspend fun getOrderByNumber(number: String): OrderEntity?
    
    @Query("SELECT * FROM orders WHERE state = :state")
    fun getOrdersByState(state: String): Flow<List<OrderEntity>>
    
    @Insert
    suspend fun insertOrder(order: OrderEntity): Long
    
    @Insert
    suspend fun insertOrders(orders: List<OrderEntity>)
    
    @Update
    suspend fun updateOrder(order: OrderEntity)
    
    @Delete
    suspend fun deleteOrder(order: OrderEntity)
    
    @Query("DELETE FROM orders WHERE id = :id")
    suspend fun deleteOrderById(id: Long)
    
    @Query("DELETE FROM orders")
    suspend fun deleteAllOrders()
}
