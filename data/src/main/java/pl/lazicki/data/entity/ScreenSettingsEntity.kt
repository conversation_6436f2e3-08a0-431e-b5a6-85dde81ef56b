package pl.lazicki.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "screen_settings")
data class ScreenSettingsEntity(
    @PrimaryKey
    val id: Int = 1,
    val screenOrientation: Int,
    val maxOrderNumber: Int = 99,
    val lastConnectedDeviceAddress: String? = null,
    val lastConnectedDeviceName: String? = null,
    val fontSize: Int = 32,
    val orderWidth: Int = 72,
    val titleInProgress: String = "Zamówione",
    val titleReady: String = "Odbierz"
)
