package pl.lazicki.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import android.content.Context
import pl.lazicki.data.dao.OrderDao
import pl.lazicki.data.dao.ScreenSettingsDao
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.ScreenSettingsEntity

@Database(
    entities = [
        ScreenSettingsEntity::class,
        OrderEntity::class
    ],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun screenSettingsDao(): ScreenSettingsDao
    abstract fun orderDao(): OrderDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    "order_system_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
