package pl.lazicki.data.repository

import kotlinx.coroutines.flow.Flow
import pl.lazicki.data.dao.ScreenSettingsDao
import pl.lazicki.data.entity.ScreenSettingsEntity
import javax.inject.Inject
import javax.inject.Singleton

typealias DeviceAddress = String
typealias DeviceName = String

@Singleton
class ScreenSettingsRepository @Inject constructor(
    private val screenSettingsDao: ScreenSettingsDao
) {

    private var deviceName: String = "KolejkaSystem.pl"

    suspend fun getDefaultScreenSettings(): ScreenSettingsEntity {
        val default = ScreenSettingsEntity(
            id = 1,
            screenOrientation = 0,
            maxOrderNumber = 99,
            orderWidth = 72,
            lastConnectedDeviceAddress = null,
            lastConnectedDeviceName = null,
            fontSize = 32,
            titleInProgress = "Zamówione",
            titleReady = "Odbierz"
        )

        if (screenSettingsDao.getScreenSettings() == null) {
            screenSettingsDao.insertScreenSettings(default)
        }
        return default
    }

    fun observeScreenSettings(): Flow<ScreenSettingsEntity?> {
        return screenSettingsDao.observeScreenSettings()
    }

    suspend fun getScreenSettings(): ScreenSettingsEntity {
        return screenSettingsDao.getScreenSettings() ?: getDefaultScreenSettings()
    }

    suspend fun updateScreenOrientation(screenOrientation: Int) {
        val settings = getScreenSettings().copy(screenOrientation = screenOrientation)
        screenSettingsDao.updateScreenSettings(settings)
    }

    suspend fun updateMaxOrderNumber(maxOrderNumber: Int) {
        val settings = getScreenSettings().copy(maxOrderNumber = maxOrderNumber)
        screenSettingsDao.updateScreenSettings(settings)
    }

    suspend fun updateFontSize(fontSize: Int) {
        val settings = getScreenSettings().copy(fontSize = fontSize)
        screenSettingsDao.updateScreenSettings(settings)
    }

    suspend fun updateOrderWidth(orderWidth: Int) {
        val settings = getScreenSettings().copy(orderWidth = orderWidth)
        screenSettingsDao.updateScreenSettings(settings)
    }

    suspend fun updateTitleInProgress(titleInProgress: String) {
        val settings = getScreenSettings().copy(titleInProgress = titleInProgress)
        screenSettingsDao.updateScreenSettings(settings)
    }

    suspend fun updateTitleReady(titleReady: String) {
        val settings = getScreenSettings().copy(titleReady = titleReady)
        screenSettingsDao.updateScreenSettings(settings)
    }

    suspend fun saveLastConnectedDevice(deviceAddress: String?, deviceName: String?) {
        val currentSettings = getScreenSettings()
        val settings = currentSettings.copy(
            lastConnectedDeviceAddress = deviceAddress,
            lastConnectedDeviceName = deviceName ?: "Unknown Device"
        )
        screenSettingsDao.updateScreenSettings(settings)
    }

    suspend fun getLastConnectedDevice(): Pair<DeviceAddress, DeviceName?>? {
        val settings = getScreenSettings()
        return if (settings.lastConnectedDeviceAddress != null) {
            settings.lastConnectedDeviceAddress to (settings.lastConnectedDeviceName
                ?: "Unknown Device")
        } else {
            null
        }
    }

    suspend fun restoreDefaultSettings() {
        screenSettingsDao.updateScreenSettings(getDefaultScreenSettings())
    }

    suspend fun clearLastConnectedDevice() {
        val currentSettings = getScreenSettings()
        val settings = currentSettings.copy(
            lastConnectedDeviceAddress = null,
            lastConnectedDeviceName = null
        )
        screenSettingsDao.updateScreenSettings(settings)
    }

    fun setDeviceName(deviceName: String) {
        this.deviceName = deviceName
    }

    fun getDeviceName() = deviceName
}
