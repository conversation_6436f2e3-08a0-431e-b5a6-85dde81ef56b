package pl.lazicki.data.repository

import kotlinx.coroutines.flow.Flow
import pl.lazicki.data.dao.ScreenSettingsDao
import pl.lazicki.data.entity.ScreenSettingsEntity

class ScreenSettingsRepository(
    private val screenSettingsDao: ScreenSettingsDao
) {
    
    fun getScreenSettings(): Flow<ScreenSettingsEntity?> {
        return screenSettingsDao.getScreenSettings()
    }
    
    suspend fun getScreenSettingsOnce(): ScreenSettingsEntity? {
        return screenSettingsDao.getScreenSettingsOnce()
    }
    
    suspend fun saveScreenSettings(screenOrientation: String) {
        val settings = ScreenSettingsEntity(
            id = 1,
            screenOrientation = screenOrientation
        )
        screenSettingsDao.insertScreenSettings(settings)
    }
    
    suspend fun updateScreenSettings(screenOrientation: String) {
        val settings = ScreenSettingsEntity(
            id = 1,
            screenOrientation = screenOrientation
        )
        screenSettingsDao.updateScreenSettings(settings)
    }
    
    suspend fun deleteScreenSettings() {
        screenSettingsDao.deleteAllScreenSettings()
    }
}
