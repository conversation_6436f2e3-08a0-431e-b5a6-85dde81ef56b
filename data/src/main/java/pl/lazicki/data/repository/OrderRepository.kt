package pl.lazicki.data.repository

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import pl.lazicki.data.dao.OrderDao
import pl.lazicki.data.dao.ScreenSettingsDao
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState
import pl.lazicki.data.entity.ScreenSettingsEntity
import javax.inject.Inject
import javax.inject.Singleton

typealias NewOrderNumber = Int
typealias MaxOrderNumber = Int

@Singleton
class OrderRepository @Inject constructor(
    private val orderDao: OrderDao,
    private val screenSettingsRepository: ScreenSettingsRepository
) {

    fun observeOrderNumber(): Flow<Pair<NewOrderNumber, MaxOrderNumber>> {
        return combine(
            orderDao.observeLastOrderNumber(),
            screenSettingsRepository.observeScreenSettings().map {
                it?.maxOrderNumber
                    ?: screenSettingsRepository.getDefaultScreenSettings().maxOrderNumber
            },
        ) { lastOrderNumber, maxOrderNumber ->
            lastOrderNumber to maxOrderNumber
        }
    }

    fun observeAllOrders(): Flow<List<OrderEntity>> {
        return orderDao.observeAllOrders()
    }

    fun getAllOrders(): List<OrderEntity> {
        return orderDao.getAllOrders()
    }

    suspend fun getOrderById(id: Long): OrderEntity? {
        return orderDao.getOrderById(id)
    }

    suspend fun getOrderByNumber(number: Int): OrderEntity? {
        return orderDao.getOrderByNumber(number)
    }

    suspend fun createOrder(number: Int, state: OrderState): Long {
        val order = OrderEntity(
            number = number,
            state = state
        )
        return orderDao.insertOrder(order)
    }

    suspend fun synchronizeOrders(orders: List<OrderEntity>) {
        println("[bluetoothController] synchronizeOrders inside")

        val currentOrders = orderDao.getAllOrders()
        println("[bluetoothController] synchronizeOrders orders: ${orders}\ncurrentOrders: $currentOrders")
        orders.forEach { order ->
            val existingOrder = currentOrders.find { it.number == order.number }
            if (existingOrder != null) {
                updateOrder(order.copy(id = existingOrder.id))
            } else {
                orderDao.insertOrder(order)
            }
        }
    }

    suspend fun updateOrderState(id: Long, newState: OrderState) {
        val existingOrder = orderDao.getOrderById(id)
        existingOrder?.let { order ->
            val updatedOrder = order.copy(state = newState)
            updateOrder(updatedOrder)
        }
    }

    suspend fun deleteAllOrders() {
        orderDao.deleteAllOrders()
    }

    private suspend fun updateOrder(order: OrderEntity) {
        orderDao.updateOrder(order)

        if (order.state == OrderState.Deleted) {
            val allCollected = orderDao.getAllCollectedOrders()
            if (allCollected.size > 5) {
                allCollected.firstOrNull()?.id?.let { orderDao.deleteOrderById(it) }
            }
        }
    }
}
