package pl.lazicki.data.repository

import kotlinx.coroutines.flow.Flow
import pl.lazicki.data.dao.OrderDao
import pl.lazicki.data.entity.OrderEntity

class OrderRepository(
    private val orderDao: OrderDao
) {
    
    fun getAllOrders(): Flow<List<OrderEntity>> {
        return orderDao.getAllOrders()
    }
    
    suspend fun getOrderById(id: Long): OrderEntity? {
        return orderDao.getOrderById(id)
    }
    
    suspend fun getOrderByNumber(number: String): OrderEntity? {
        return orderDao.getOrderByNumber(number)
    }
    
    fun getOrdersByState(state: String): Flow<List<OrderEntity>> {
        return orderDao.getOrdersByState(state)
    }
    
    suspend fun createOrder(number: String, state: String): Long {
        val order = OrderEntity(
            number = number,
            state = state
        )
        return orderDao.insertOrder(order)
    }
    
    suspend fun createOrders(orders: List<Pair<String, String>>) {
        val orderEntities = orders.map { (number, state) ->
            OrderEntity(number = number, state = state)
        }
        orderDao.insertOrders(orderEntities)
    }
    
    suspend fun updateOrder(id: Long, number: String, state: String) {
        val order = OrderEntity(
            id = id,
            number = number,
            state = state
        )
        orderDao.updateOrder(order)
    }
    
    suspend fun updateOrderState(id: Long, newState: String) {
        val existingOrder = orderDao.getOrderById(id)
        existingOrder?.let { order ->
            val updatedOrder = order.copy(state = newState)
            orderDao.updateOrder(updatedOrder)
        }
    }
    
    suspend fun deleteOrder(id: Long) {
        orderDao.deleteOrderById(id)
    }
    
    suspend fun deleteAllOrders() {
        orderDao.deleteAllOrders()
    }
}
