package pl.lazicki.data.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import pl.lazicki.data.dao.OrderDao
import pl.lazicki.data.dao.ScreenSettingsDao
import pl.lazicki.data.database.AppDatabase
import pl.lazicki.data.repository.ScreenSettingsRepository
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return AppDatabase.getDatabase(context)
    }

    @Provides
    @Singleton
    fun provideScreenSettingsDao(appDatabase: AppDatabase): ScreenSettingsDao {
        return appDatabase.screenSettingsDao()
    }


    @Provides
    @Singleton
    fun provideOrderDao(appDatabase: AppDatabase): OrderDao {
        return appDatabase.orderDao()
    }

    @Provides
    @Singleton
    fun provideScreenSettingsRepository(screenSettingsDao: ScreenSettingsDao): ScreenSettingsRepository {
        return ScreenSettingsRepository(screenSettingsDao)
    }
}