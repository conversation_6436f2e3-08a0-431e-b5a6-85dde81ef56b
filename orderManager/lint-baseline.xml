<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.9.3" type="baseline" client="gradle" dependencies="false" name="AGP (8.9.3)" variant="all" version="8.9.3">

    <issue
        id="UnusedAttribute"
        message="Attribute `usesPermissionFlags` is only used in API level 31 and higher (current min is 29)"
        errorLine1="        android:usesPermissionFlags=&quot;neverForLocation&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="7"
            column="9"/>
    </issue>

    <issue
        id="RedundantLabel"
        message="Redundant label can be removed"
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.black` appears to be unused"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.white` appears to be unused"
        errorLine1="    &lt;color name=&quot;white&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="9"
            column="12"/>
    </issue>

</issues>
