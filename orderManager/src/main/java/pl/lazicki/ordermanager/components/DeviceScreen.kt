package pl.lazicki.ordermanager.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import pl.lazicki.bluetooth.domain.BluetoothDevice
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState

@Composable
fun BluetoothDeviceList(
    isConnecting: <PERSON>olean,
    isConnected: Boolean,
    errorMessage: String?,
    lastConnectedDevice: BluetoothDevice?,
    pairedDevices: List<BluetoothDevice>,
    scannedDevices: List<BluetoothDevice>,
    onClick: (BluetoothDevice) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
    ) {
        if (lastConnectedDevice != null) {
            item {
                Text(
                    text = "Ostatnio połączone urządzenie",
                    fontWeight = FontWeight.Bold,
                    fontSize = 22.sp,
                    modifier = Modifier.padding(16.dp)
                )
            }
            item {
                val fontColor = when {
                    isConnected -> Color(0xFF79B407)
                    isConnecting -> MaterialTheme.colorScheme.primary
                    errorMessage != null -> MaterialTheme.colorScheme.error
                    else -> Color.Gray
                }

                Text(
                    text = lastConnectedDevice.name ?: "(brak nazwy)",
                    fontWeight = FontWeight.Bold,
                    fontSize = 20.sp,
                    color = fontColor,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(top = 8.dp)
                )

                val state = when {
                    isConnecting -> "Łączenie..."
                    isConnected -> "Połączono"
                    errorMessage != null -> "Błąd: $errorMessage"
                    else -> "Rozłączono"
                }

                Text(
                    text = "Stan: $state".uppercase(),
                    fontWeight = FontWeight.Bold,
                    fontSize = 12.sp,
                    color = Color.Black,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                )
                if ((!isConnected || errorMessage != null) && !isConnecting) {
                    Column {
                        Text(
                            text = "Podpowiedź:",
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp,
                            color = Color.Black,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp)
                                .padding(top = 20.dp)
                        )
                        Text(
                            text = "Upewnij sie, że monitor jest włączony.",
                            fontWeight = FontWeight.Normal,
                            fontSize = 14.sp,
                            color = Color.Black,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp)
                                .padding(top = 0.dp)
                        )
                    }

                    OutlinedButton(
                        onClick = { onClick(lastConnectedDevice) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 24.dp)
                    ) {
                        Text(
                            text = "Monitor jest włączony\nSpróbuj ponownie",
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp,
                            color = Color.Black,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp)
                        )
                    }


                }
            }
        } else if (pairedDevices.any { it.name?.contains("KolejkaSystem.pl") == true }) {
            item {
                Text(
                    text = "Zapamiętane urządzenia",
                    fontWeight = FontWeight.Bold,
                    fontSize = 24.sp,
                    modifier = Modifier.padding(16.dp)
                )
            }
            items(pairedDevices) { device ->
                val (fontWeight, fontColor) = if (device.name?.contains("KolejkaSystem.pl") == true) {
                    FontWeight.Bold to MaterialTheme.colorScheme.primary
                } else {
                    FontWeight.Normal to Color.Gray
                }
                val fontSize = if (device.name?.contains("KolejkaSystem.pl") == true) {
                    20.sp
                } else {
                    16.sp
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                        .clickable { onClick(device) }
                ) {
                    Text(
                        text = device.name ?: "(brak nazwy)",
                        fontWeight = fontWeight,
                        fontSize = fontSize,
                        color = fontColor,
                        modifier = Modifier
                            .fillMaxWidth()
                    )
                    if (device.name?.contains("KolejkaSystem.pl") == true) {
                        Text(
                            text = "Kliknij tutaj żeby się połączyć",
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier
                                .fillMaxWidth()
                        )
                    }
                }
            }
        }
        //bez else if - jesli chciałby znalezc inny ekran
        if (scannedDevices.any { it.name?.contains("KolejkaSystem.pl") == true }) {
            item {
                Text(
                    text = "Nowe urządzenia",
                    fontWeight = FontWeight.Bold,
                    fontSize = 24.sp,
                    modifier = Modifier.padding(16.dp)
                )
            }
            items(scannedDevices) { device ->
                val (fontWeight, fontColor) = if (device.name?.contains("KolejkaSystem.pl") == true) {
                    FontWeight.Bold to MaterialTheme.colorScheme.primary
                } else {
                    FontWeight.Normal to Color.Gray
                }
                val fontSize = if (device.name?.contains("KolejkaSystem.pl") == true) {
                    20.sp
                } else {
                    16.sp
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                        .clickable { onClick(device) }
                ) {
                    Text(
                        text = device.name ?: "(brak nazwy)",
                        fontWeight = fontWeight,
                        fontSize = fontSize,
                        color = fontColor,
                        modifier = Modifier
                            .fillMaxWidth()
                    )
                    if (device.name?.contains("KolejkaSystem.pl") == true) {
                        Text(
                            text = "Kliknij tutaj żeby się połączyć",
                            fontWeight = FontWeight.Bold,
                            fontSize = 14.sp,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier
                                .fillMaxWidth()
                        )
                    }
                }
            }
        }
    }
}

@Preview(
    widthDp = 540,
    heightDp = 960,
    device = Devices.TV_1080p,
    showBackground = true,
    backgroundColor = 0xFFFFFFFF
)
@Composable
fun BluetoothDeviceListPreview() {
    BluetoothDeviceList(
        isConnecting = false,
        isConnected = false,
        errorMessage = null,
        lastConnectedDevice = BluetoothDevice("KolejkaSystem.pl - #2231", "12:34:56:78:90:AB"),
        pairedDevices = emptyList(),
        scannedDevices = emptyList(),
        onClick = {}
    )
}
