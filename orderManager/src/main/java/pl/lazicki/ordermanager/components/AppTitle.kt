package pl.lazicki.ordermanager.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import pl.lazicki.ordermanager.OrderManagerTheme
import pl.lazicki.ordermanager.ui.destinations.OrdersScreenDestination

@Composable
fun AppTitle(onClick: () -> Unit) {
    val interactionSource = remember { MutableInteractionSource() }

    Text(
        text = buildAnnotatedString {
            withStyle(style = SpanStyle(color = Color.White)) {
                append("Kolejka")
            }
            withStyle(style = SpanStyle(color = Color(0xFFf97316))) {
                append("System")
            }
            withStyle(style = SpanStyle(color = Color.White)) {
                append(".pl")
            }
        },
        fontWeight = FontWeight.Bold,
        textAlign = TextAlign.Center,
        fontSize = 24.sp,
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = interactionSource,
                indication = null
            ) { onClick() }
    )
}

@Preview
@Composable
fun AppTitlePreview() {
    OrderManagerTheme {
        AppTitle {

        }
    }
}
