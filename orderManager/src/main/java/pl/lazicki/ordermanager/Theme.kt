package pl.lazicki.ordermanager

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import pl.lazicki.design.ColorScheme
import pl.lazicki.design.typography.MobileTypography

@Composable
fun OrderManagerTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    MaterialTheme(
        colorScheme = ColorScheme,
        typography = MobileTypography,
        content = content
    )
}