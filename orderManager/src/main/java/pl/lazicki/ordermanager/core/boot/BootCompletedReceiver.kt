package pl.lazicki.ordermanager.core.boot

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import pl.lazicki.ordermanager.ui.main.MainActivity

/**
 * BroadcastReceiver, który nasłuchuje zdarzenia BOOT_COMPLETED
 * i uruchamia aplikację po starcie systemu.
 */
class BootCompletedReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootCompletedReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        Log.d(TAG, "Boot completed received: ${intent.action}")
        
        if (intent.action == Intent.ACTION_BOOT_COMPLETED ||
            intent.action == "android.intent.action.QUICKBOOT_POWERON") {
            
            Log.d(TAG, "Starting application after boot...")
            
            // Tworzymy intent do uruchomienia MainActivity
            val startAppIntent = Intent(context, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            // Uruchamiamy aplikację
            context.startActivity(startAppIntent)
        }
    }
}