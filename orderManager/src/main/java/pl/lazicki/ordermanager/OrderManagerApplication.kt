package pl.lazicki.ordermanager

import android.app.Application
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import pl.lazicki.bluetooth.di.bluetoothModule
import pl.lazicki.data.dataModule
import pl.lazicki.ordermanager.di.appModule

class OrderManagerApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        startKoin {
            androidLogger()
            androidContext(this@OrderManagerApplication)
            modules(
                bluetoothModule,
                appModule,
                dataModule
            )
        }
    }
}
