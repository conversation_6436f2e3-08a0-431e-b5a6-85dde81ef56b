package pl.lazicki.ordermanager.ui.welcome

import pl.lazicki.bluetooth.domain.BluetoothDeviceDomain

object WelcomeContract {
    data class State(
        val scannedDevices: List<BluetoothDeviceDomain> = emptyList(),
        val pairedDevices: List<BluetoothDeviceDomain> = emptyList(),
        val selectedDeviceAddress: String? = null,
        val lastConnectedDevice: BluetoothDeviceDomain? = null,
        val isConnected: Boolean = false,
        val isConnecting: Boolean = false,
        val isSearching: Boolean = false,
        val errorMessage: String? = null,
    )

    sealed interface Action {
        data class OnDeviceClick(val device: BluetoothDeviceDomain) : Action
        data object OnRemoveLastConnectionClick : Action
        data object OnStartScan : Action
        data object OnStopScan : Action
    }

    sealed interface Effect {
        data object NavigateToOrderScreen : Effect
    }
}
