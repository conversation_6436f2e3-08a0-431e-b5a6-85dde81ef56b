package pl.lazicki.ordermanager.ui.welcome

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.bluetooth.domain.BluetoothDeviceDomain
import pl.lazicki.bluetooth.domain.ConnectionResult
import pl.lazicki.data.repository.ScreenSettingsRepository
import pl.lazicki.ordermanager.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class WelcomeViewModel @Inject constructor(
    private val screenSettingsRepository: ScreenSettingsRepository,
    private val bluetoothController: BluetoothController,
) : MviViewModel<WelcomeContract.State, WelcomeContract.Action, WelcomeContract.Effect>(
    initialState = WelcomeContract.State()
) {

    private var deviceConnectionJob: Job? = null
    private var cyclicScanJob: Job? = null

    init {
        observePaired()
        observeScanned()
        viewModelScope.launch(Dispatchers.IO) {
            bluetoothController.isConnected.collect { isConnected ->
                val lastConnectedDevice = screenSettingsRepository.getLastConnectedDevice()?.let {
                    BluetoothDeviceDomain(address = it.first, name = it.second ?: "Unknown Device")
                }
                updateState {
                    copy(
                        isConnected = isConnected,
                        lastConnectedDevice = lastConnectedDevice
                    )
                }
                if (isConnected) {
                    // Jeśli połączono, przerwij cykliczne skanowanie
                    stopCyclicScanProcedure()
                }
            }
        }

        bluetoothController.errors.onEach { error ->
            updateState { copy(errorMessage = error) }
        }.launchIn(viewModelScope)

        // Rozpocznij cykliczne skanowanie przy inicjalizacji, jeśli nie ma połączenia
        if (!state.value.isConnected) {
            startCyclicScanProcedure()
        }
    }

    override fun onAction(action: WelcomeContract.Action) {
        when (action) {
            is WelcomeContract.Action.OnDeviceClick -> {
                connectToDevice(action.device)
            }

            is WelcomeContract.Action.OnStartScan -> {
                startCyclicScanProcedure()
            }

            is WelcomeContract.Action.OnStopScan -> {
                stopCyclicScanProcedure()
            }

            WelcomeContract.Action.OnRemoveLastConnectionClick -> {
                viewModelScope.launch(Dispatchers.IO) {
                    screenSettingsRepository.saveLastConnectedDevice(
                        deviceAddress = null,
                        deviceName = null
                    )
                }
            }
        }
    }

    private fun observePaired() {
        viewModelScope.launch {
            bluetoothController.pairedDevices.collect { pairedDevices ->
                updateState { copy(pairedDevices = pairedDevices) }
            }
        }
    }


    private fun observeScanned() {
        viewModelScope.launch {
            bluetoothController.scannedDevices.collect { scannedDevicesFromController ->
                updateState {
                    val currentPairedAddresses = pairedDevices.map { it.address }
                    copy(
                        scannedDevices = scannedDevicesFromController
                            .filterNot { scannedDevice ->
                                currentPairedAddresses.contains(
                                    scannedDevice.address
                                )
                            }
                    )
                }
            }
        }
    }

    private fun connectToDevice(device: BluetoothDeviceDomain) {
        stopCyclicScanProcedure() // Zatrzymaj cykl przed próbą połączenia
        updateState { copy(isConnecting = true, selectedDeviceAddress = device.address) }
        deviceConnectionJob = bluetoothController
            .connectToDevice(device)
            .listen()
    }

    private fun startCyclicScanProcedure() {
        cyclicScanJob?.cancel()

        cyclicScanJob = viewModelScope.launch {
            while (isActive && !state.value.isConnected) {
                updateState { copy(isSearching = true, errorMessage = null) }
                bluetoothController.startDiscovery()

                delay(10000L) // Skanuj przez 10 sekund

                // Zawsze zatrzymaj discovery, nawet jeśli pętla zaraz się zakończy
                if (state.value.isSearching) { // Sprawdź, czy stan nie zmienił się w międzyczasie
                    updateState { copy(isSearching = false) }
                    bluetoothController.stopDiscovery()
                }

                // Sprawdź warunki przed przerwą, aby móc szybciej wyjść z pętli
                if (!isActive ||
                    state.value.scannedDevices.any { it.name?.contains("KolejkaSystem.pl") == true } ||
                    state.value.pairedDevices.any { it.name?.contains("KolejkaSystem.pl") == true } ||
                    state.value.isConnected
                ) break

                delay(10000L) // Czekaj 10 sekund przed kolejnym skanowaniem

                // Sprawdź warunki ponownie przed rozpoczęciem nowego cyklu
                if (!isActive ||
                    state.value.scannedDevices.any { it.name?.contains("KolejkaSystem.pl") == true } ||
                    state.value.pairedDevices.any { it.name?.contains("KolejkaSystem.pl") == true } ||
                    state.value.isConnected
                ) break
            }
            if (state.value.isSearching) { // Upewnij się, że discovery jest wyłączone
                updateState { copy(isSearching = false) }
                bluetoothController.stopDiscovery()
            }
        }
    }

    private fun stopCyclicScanProcedure() {
        cyclicScanJob?.cancel()
        cyclicScanJob = null
        if (state.value.isSearching) {
            updateState { copy(isSearching = false) }
            bluetoothController.stopDiscovery()
        }
    }

    private fun Flow<ConnectionResult>.listen(): Job {
        return onEach { result ->
            when (result) {
                ConnectionResult.ConnectionEstablished -> {
                    val currentDevice =
                        state.value.pairedDevices.find { it.address == state.value.selectedDeviceAddress }
                            ?: state.value.scannedDevices.find { it.address == state.value.selectedDeviceAddress }
                    currentDevice?.let { device ->
                        viewModelScope.launch(Dispatchers.IO) {
                            screenSettingsRepository.saveLastConnectedDevice(
                                deviceAddress = device.address,
                                deviceName = device.name
                            )
                        }
                    }
                    updateState {
                        copy(
                            isConnected = true,
                            isConnecting = false,
                            errorMessage = null
                        )
                    }
                    pushEffect(WelcomeContract.Effect.NavigateToOrderScreen)
                }

                is ConnectionResult.TransferSucceeded -> {}

                is ConnectionResult.Error -> {
                    updateState {
                        copy(
                            isConnected = false,
                            isConnecting = false,
                            errorMessage = result.message
                        )
                    }
                }
            }
        }
            .catch { throwable ->
                bluetoothController.closeConnection()
                updateState {
                    copy(
                        isConnected = false,
                        isConnecting = false,
                        errorMessage = throwable.message ?: "Błąd połączenia"
                    )
                }
            }
            .launchIn(viewModelScope)
    }

    override fun onCleared() {
        super.onCleared()
        deviceConnectionJob?.cancel()
        cyclicScanJob?.cancel()
    }
}
