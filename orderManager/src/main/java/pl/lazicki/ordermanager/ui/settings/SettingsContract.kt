package pl.lazicki.ordermanager.ui.settings

object SettingsContract {
    data class State(
        val appVersion: String,
        val maxOrderNumber: Int = 99,
        val fontSize: Int = 32,
        val orderWidth: Int = 72,
        val titleInProgress: String = "Zamówione",
        val titleReady: String = "Odbierz",
        val screenOrientation: Int = 0,
        val isLoading: <PERSON>olean = false,
        val isSuccess: Boolean = false,
        val error: String? = null
    )

    sealed interface Action {
        data object IncreaseFontSize : Action
        data object DecreaseFontSize : Action
        data object IncreaseOrderWidth : Action
        data object DecreaseOrderWidth : Action
        data class UpdateMaxOrderNumber(val maxOrderNumber: Int) : Action
        data class UpdateTitleInProgress(val title: String) : Action
        data class UpdateTitleReady(val title: String) : Action
        data object RotateScreen : Action
        data object ClearOrders : Action
        data object ResetToDefault : Action
        data object OpenBluetoothSettings : Action
    }

    sealed interface Effect {
        data object NavigateBack : Effect
        data object NavigateToBluetoothSettings : Effect
    }
}
