package pl.lazicki.ordermanager.ui.main

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.rememberTopAppBarState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.NavOptions
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navOptions
import com.ramcosta.composedestinations.DestinationsNavHost
import com.ramcosta.composedestinations.navigation.dependency
import com.ramcosta.composedestinations.navigation.popUpTo
import pl.lazicki.ordermanager.OrderManagerTheme
import pl.lazicki.ordermanager.components.AppTitle
import pl.lazicki.ordermanager.core.mvi.EffectCollector
import pl.lazicki.ordermanager.ui.NavGraphs
import pl.lazicki.ordermanager.ui.appCurrentDestinationAsState
import pl.lazicki.ordermanager.ui.destinations.OrdersScreenDestination
import pl.lazicki.ordermanager.ui.destinations.SettingsScreenDestination
import pl.lazicki.ordermanager.ui.destinations.WelcomeScreenDestination
import pl.lazicki.ordermanager.ui.startAppDestination

@Composable
fun MainScreen(
    viewModel: MainViewModel = hiltViewModel()
) {
    val navController = rememberNavController()
    val uiState: MainContract.State by viewModel.state.collectAsStateWithLifecycle()

    EffectCollector(effect = viewModel.effect) { effect ->
        when (effect) {
            MainContract.Effect.NavigateToOrderScreen -> {
                if (navController.currentDestination?.route != OrdersScreenDestination.route) {
                    navController.navigate(OrdersScreenDestination.route)
                }
            }

            MainContract.Effect.NavigateToWelcomeScreen -> {
                if (navController.currentDestination?.route != WelcomeScreenDestination.route) {
                    navController.navigate(WelcomeScreenDestination.route)
                }
            }
        }
    }

    MainScreenContent(
        navController = navController,
        uiState = uiState,
        onAction = viewModel::onAction
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreenContent(
    navController: NavHostController,
    uiState: MainContract.State,
    onAction: (MainContract.Action) -> Unit,
) {
    val scrollBehavior = TopAppBarDefaults.pinnedScrollBehavior(rememberTopAppBarState())
    val currentScreen = navController.appCurrentDestinationAsState().value
        ?: NavGraphs.root.startAppDestination
    Surface(
        modifier = Modifier
            .fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Scaffold(
            modifier = Modifier.imePadding(),
            containerColor = MaterialTheme.colorScheme.background,
            topBar = {
                CenterAlignedTopAppBar(
                    colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer,
                        titleContentColor = MaterialTheme.colorScheme.primary,
                    ),
                    title = {
                        Column {
                            AppTitle {
                                if (currentScreen != OrdersScreenDestination) {
                                    navController.navigate(
                                        route = OrdersScreenDestination.route,
                                        navOptions = navOptions {
                                            popUpTo(OrdersScreenDestination.route) {
                                                inclusive = true
                                            }
                                            launchSingleTop = true
                                        }
                                    )
                                }
                            }
                            if (uiState.isConnecting) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.Center,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                ) {
                                    CircularProgressIndicator(
                                        strokeWidth = 2.dp,
                                        modifier = Modifier
                                            .size(22.dp)
                                            .padding(4.dp)
                                    )
                                    Text(
                                        text = "Łączenie z ekranem...",
                                        textAlign = TextAlign.Center,
                                        maxLines = 1,
                                        fontWeight = FontWeight.Bold,
                                        color = Color.White,
                                        fontSize = 12.sp,
                                    )
                                }
                            }
                        }
                    },
                    navigationIcon = {
                        if (currentScreen != OrdersScreenDestination) {
                            IconButton(
                                modifier = Modifier
                                    .size(42.dp)
                                    .padding(start = 8.dp),
                                onClick = { navController.popBackStack() }) {
                                Icon(
                                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                    contentDescription = "Wstecz",
                                    tint = Color.White
                                )
                            }
                        } else {
                            Spacer(
                                modifier = Modifier
                                    .size(42.dp)
                                    .padding(start = 8.dp)
                            )
                        }
                    },
                    actions = {
                        if (currentScreen == OrdersScreenDestination) {
                            IconButton(
                                modifier = Modifier
                                    .size(42.dp)
                                    .padding(end = 8.dp),
                                onClick = { navController.navigate(SettingsScreenDestination.route) }) {
                                Icon(
                                    imageVector = Icons.Filled.Settings,
                                    contentDescription = "Ustawienia",
                                    tint = Color.White
                                )
                            }
                        } else {
                            Spacer(
                                modifier = Modifier
                                    .size(42.dp)
                                    .padding(end = 8.dp)
                            )
                        }
                    },
                    scrollBehavior = scrollBehavior,
                )
            },
        ) { padding ->
            Surface(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding),
                color = MaterialTheme.colorScheme.background
            ) {
                DestinationsNavHost(
                    navGraph = NavGraphs.root,
                    navController = navController,
                    dependenciesContainerBuilder = {
                        dependency(hiltViewModel<MainViewModel>())
                    }
                )
            }
        }
    }
}

@Preview
@Composable
fun MainPreview() {
    OrderManagerTheme {
        MainScreenContent(
            navController = rememberNavController(),
            uiState = MainContract.State(screenOrientation = 0, isConnected = false),
            onAction = {}
        )
    }
}