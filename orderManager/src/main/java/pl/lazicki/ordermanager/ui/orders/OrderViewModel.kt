package pl.lazicki.ordermanager.ui.orders

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.bluetooth.domain.Command
import pl.lazicki.bluetooth.domain.CommandType
import pl.lazicki.data.entity.OrderState
import pl.lazicki.data.repository.OrderRepository
import pl.lazicki.data.repository.ScreenSettingsRepository
import pl.lazicki.ordermanager.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class OrderViewModel @Inject constructor(
    private val bluetoothController: BluetoothController,
    private val orderRepository: OrderRepository,
    private val screenSettingsRepository: ScreenSettingsRepository
) : MviViewModel<
        OrderContract.State,
        OrderContract.Action,
        OrderContract.Effect>(
    initialState = OrderContract.State()
) {

    init {
        observeBluetoothSetup()
        observeOrders()
        observeLastOrderNumber()
    }

    override fun onAction(action: OrderContract.Action) {
        when (action) {
            is OrderContract.Action.OnNewOrder -> {
                viewModelScope.launch {
                    orderRepository.createOrder(action.orderNumber, OrderState.InProgress)
                    orderRepository.getOrderByNumber(action.orderNumber)?.let { addedOrder ->
                        bluetoothController.addUiChange(addedOrder)
                    }
                }
            }

            is OrderContract.Action.OnUpdateOrder -> {
                viewModelScope.launch {
                    orderRepository.updateOrderState(action.orderId, action.newState)
                    orderRepository.getOrderById(action.orderId)?.let { updatedOrder ->
                        bluetoothController.addUiChange(updatedOrder)
                    }
                }
            }
        }
    }

    @OptIn(FlowPreview::class)
    private fun observeLastOrderNumber() {
        viewModelScope.launch(Dispatchers.IO) {
            orderRepository.observeOrderNumber()
                .debounce(100)
                .collectLatest { (lastOrderNumber, maxOrderNumber) ->
                    val newOrderNumber = if (lastOrderNumber < maxOrderNumber) lastOrderNumber + 1 else 1
                    updateState { copy(nextOrderNumber = newOrderNumber) }
                }
        }
    }

    private fun observeOrders() {
        viewModelScope.launch {
            orderRepository.observeAllOrders().collect { orders ->
                updateState {
                    copy(
                        orders = orders.sortedByDescending { it.state },
                        isOrdersLoading = false,
                    )
                }

                if (state.value.isConnected) {
                    delay(500)
                    val success = bluetoothController.trySendCommand(
                        Command(
                            type = CommandType.SYNCHRONIZE,
                            orders = orders,
                        )
                    )
                    println("[OrderViewModel][bluetoothController] observeOrders trySendCommand, success: $success")
                }
            }
        }
    }

    private fun observeBluetoothSetup() {
        bluetoothController.isConnected.onEach { isConnected ->
            updateState { copy(isConnected = isConnected) }
            println("[OrderViewModel][bluetoothController] isConnected: $isConnected")

            if (isConnected) {
                delay(500)
                syncAllOrders()

                viewModelScope.launch(Dispatchers.IO) {
                    bluetoothController.currentConnectedDevice?.let { device ->
                        screenSettingsRepository.saveLastConnectedDevice(
                            deviceAddress = device.address,
                            deviceName = device.name
                        )
                    }
                }
            }
        }.launchIn(viewModelScope)

        bluetoothController.errors.onEach { error ->
            updateState { copy(errorMessage = error) }
            println("[OrderViewModel][bluetoothController] error: $error")
        }.launchIn(viewModelScope)
    }

    private fun syncAllOrders() {
        viewModelScope.launch(Dispatchers.IO) {
            val allOrders = orderRepository.getAllOrders()
            println("[OrderViewModel] Sending full sync with ${allOrders.size} orders")

            val success = bluetoothController.trySendCommand(
                Command(
                    type = CommandType.SYNCHRONIZE,
                    orders = allOrders,
                )
            )

            println("[OrderViewModel] Full sync sent, success: $success")
        }
    }
}
