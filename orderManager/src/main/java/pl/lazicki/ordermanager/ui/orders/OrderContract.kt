package pl.lazicki.ordermanager.ui.orders

import pl.lazicki.bluetooth.domain.BluetoothDeviceDomain
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState

object OrderContract {
    data class State(
        val scannedDevices: List<BluetoothDeviceDomain> = emptyList(),
        val pairedDevices: List<BluetoothDeviceDomain> = emptyList(),
        val isConnected: Boolean = false,
        val isConnecting: Boolean = false,
        val isOrdersLoading: Boolean = true,
        val errorMessage: String? = null,
        val orders: List<OrderEntity> = emptyList(),
        val nextOrderNumber: Int = 1,
        val appVersion: String = "1.0.0"
    )

    sealed interface Action {
        data class OnNewOrder(val orderNumber: Int) : Action
        data class OnUpdateOrder(val orderId: Long, val newState: OrderState) : Action
    }

    sealed interface Effect
}
