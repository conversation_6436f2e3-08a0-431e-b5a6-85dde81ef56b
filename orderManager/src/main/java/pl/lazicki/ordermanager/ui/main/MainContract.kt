package pl.lazicki.ordermanager.ui.main

object MainContract {

    data class State(
        val screenOrientation: Int = 0,
        val isConnected: Boolean,
        val isConnecting: Boolean = false,
        val errorMessage: String? = null,
    )

    sealed interface Action

    sealed interface Effect {
        data object NavigateToOrderScreen : Effect
        data object NavigateToWelcomeScreen : Effect
    }
}
