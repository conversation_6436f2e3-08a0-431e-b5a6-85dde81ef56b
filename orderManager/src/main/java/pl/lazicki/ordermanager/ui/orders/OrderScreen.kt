package pl.lazicki.ordermanager.ui.orders

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.outlined.CheckCircle
import androidx.compose.material.icons.outlined.Lock
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.annotation.RootNavGraph
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState

@RootNavGraph(start = true)
@Destination
@Composable
fun OrdersScreen(
    viewModel: OrderViewModel = hiltViewModel(),
) {
    val uiState: OrderContract.State by viewModel.state.collectAsStateWithLifecycle()

    OrdersScreenContent(uiState, viewModel::onAction)
}

@Composable
fun OrdersScreenContent(
    state: OrderContract.State,
    onAction: (OrderContract.Action) -> Unit
) {
    val gridState = rememberLazyGridState()
    val firstOrderId = state.orders.firstOrNull()?.id
    val firstInProgressOrderId = state.orders.firstOrNull { it.state == OrderState.InProgress }?.id
    val firstReadyOrderId = state.orders.firstOrNull { it.state == OrderState.Ready }?.id

    LaunchedEffect(state.orders.map { it.state }) {
        val readyIndex = state.orders.indexOfFirst { it.state == OrderState.Ready }
        if (readyIndex >= 0) {
            gridState.animateScrollToItem(readyIndex)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background),
    ) {
        LazyVerticalGrid(
            state = gridState,
            columns = GridCells.Fixed(1),
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(0.dp),
            horizontalArrangement = Arrangement.Center,
            contentPadding = PaddingValues(0.dp)
        ) {
            items(state.orders, key = { it.id }) { order ->
                val addTopBelt =
                    (order.id == firstReadyOrderId || order.id == firstInProgressOrderId)
                            && order.id != firstOrderId

                OrderManagerItem(
                    order = order,
                    onAction = onAction,
                    addTopBelt = addTopBelt
                )
            }
        }

        ManageOrderBottomSheet(
            uiState = state,
            onAction = onAction,
            modifier = Modifier,//.weight(1f),
        )
    }
}

@Composable
fun OrderManagerItem(
    order: OrderEntity,
    onAction: (OrderContract.Action) -> Unit,
    addTopBelt: Boolean,
) {
    val isDone = order.state == OrderState.Ready
    val (backgroundColor, borderColor) = if (isDone) {
        Color(0xFF79B407) to Color(0xFF79B407)
    } else {
        Color(0xFFFFFFFF) to Color(0xFF625C5C)
    }
    val textColor = if (isDone) {
        Color.White
    } else {
        Color.Black
    }
    val alpha = if (order.state == OrderState.Deleted) 0.25f else 1f

    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (addTopBelt) {
            Spacer(modifier = Modifier.height(18.dp))
        }

        Card(
            colors = CardDefaults.cardColors().copy(containerColor = Color(0xFFF8FAFC)),
            elevation = CardDefaults.cardElevation(2.dp),
            modifier = Modifier
                .height(100.dp)
                .padding(4.dp)
                .widthIn(min = 360.dp, max = 540.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxHeight()
            ) {
                Spacer(modifier = Modifier.weight(0.025f))
                Box(
                    modifier = Modifier
                        .widthIn(min = 64.dp, max = 72.dp)
                        .heightIn(min = 48.dp, max = 64.dp)
                        .background(backgroundColor, RoundedCornerShape(8.dp))
                        .border(1.dp, borderColor.copy(alpha = alpha), RoundedCornerShape(8.dp))
                        .padding(8.dp)
                        .weight(0.25f)
                        .alpha(alpha)
                ) {
                    Row(
                        modifier = Modifier.align(Alignment.Center)
                    ) {
                        Text(
                            text = "${order.number}",
                            color = textColor,
                            textAlign = TextAlign.Center,
                            maxLines = 1,
                            fontSize = 28.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier
                        )
                    }
                }
                Spacer(modifier = Modifier.weight(0.025f))

                val selectedColor = Color(0xEED7F3DA)
                Box(
                    modifier = Modifier
                        .widthIn(min = 92.dp, max = 100.dp)
                        .fillMaxHeight()
                        .background(if (order.state == OrderState.InProgress) selectedColor else Color.Transparent)
                        .weight(0.25f)
                        .alpha(alpha)
                        .clickable {
                            onAction(
                                OrderContract.Action.OnUpdateOrder(
                                    order.id,
                                    OrderState.InProgress
                                )
                            )
                        }
                ) {
                    val weight = if (order.state == OrderState.InProgress) {
                        FontWeight.Bold
                    } else {
                        FontWeight.Normal
                    }
                    Text(
                        text = "W trakcie".uppercase(),
                        color = Color.Black,
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp,
                        fontWeight = weight,
                        modifier = Modifier
                            .align(Alignment.Center)
                    )
                }

                Box(
                    modifier = Modifier
                        .widthIn(min = 92.dp, max = 100.dp)
                        .fillMaxHeight()
                        .background(if (order.state == OrderState.Ready) selectedColor else Color.Transparent)
                        .weight(0.25f)
                        .alpha(alpha)
                        .clickable {
                            onAction(
                                OrderContract.Action.OnUpdateOrder(
                                    order.id,
                                    OrderState.Ready
                                )
                            )
                        }
                ) {
                    val weight = if (order.state == OrderState.Ready) {
                        FontWeight.Bold
                    } else {
                        FontWeight.Normal
                    }
                    Text(
                        text = "Gotowe".uppercase(),
                        color = Color.Black,
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp,
                        fontWeight = weight,
                        modifier = Modifier
                            .align(Alignment.Center)
                    )
                }

                Box(
                    modifier = Modifier
                        .widthIn(min = 92.dp, max = 100.dp)
                        .fillMaxHeight()
                        .background(if (order.state == OrderState.Deleted) selectedColor else Color.Transparent)
                        .weight(0.25f)
                        .clickable {
                            onAction(
                                OrderContract.Action.OnUpdateOrder(
                                    order.id,
                                    OrderState.Deleted
                                )
                            )
                        }
                ) {
                    val weight = if (order.state == OrderState.Deleted) {
                        FontWeight.Bold
                    } else {
                        FontWeight.Normal
                    }
                    Text(
                        text = "Odebrane".uppercase(),
                        color = Color.Black,
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp,
                        fontWeight = weight,
                        modifier = Modifier
                            .align(Alignment.Center)
                    )
                }
            }
        }
    }
}

@Composable
private fun ManageOrderBottomSheet(
    uiState: OrderContract.State,
    onAction: (OrderContract.Action) -> Unit,
    modifier: Modifier = Modifier
) {
    val interactionSource = remember { MutableInteractionSource() }

    Card(
        shape = RoundedCornerShape(
            topStart = 12.dp,
            topEnd = 12.dp,
            bottomStart = 0.dp,
            bottomEnd = 0.dp
        ),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = MaterialTheme.colorScheme.onSurface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 6.dp),
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 16.dp)
            .clickable(
                interactionSource = interactionSource,
                indication = null
            ) { onAction(OrderContract.Action.OnNewOrder(uiState.nextOrderNumber)) },
    ) {
        Text(
            text = "Dodaj nowe zamówienie",
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
        )
        Row(
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            OrderItem(
                isOrdersLoading = uiState.isOrdersLoading,
                isLocked = false,
                fontSize = 32,
                number = uiState.nextOrderNumber.toString(),
                modifier = Modifier.width(92.dp)
            )
        }
    }
}

@Composable
fun OrderItem(
    isOrdersLoading: Boolean,
    isLocked: Boolean,
    fontSize: Int,
    number: String,
    modifier: Modifier = Modifier
) {
    val backgroundColor = MaterialTheme.colorScheme.primaryContainer
    val textColor = Color.White

    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .background(backgroundColor, RoundedCornerShape(8.dp))
            .border(1.dp, backgroundColor, RoundedCornerShape(8.dp))
            .padding(vertical = 2.dp, horizontal = 10.dp)
    ) {
        if (isOrdersLoading) {
            CircularProgressIndicator(
                strokeWidth = 2.dp,
                color = textColor,
                modifier = Modifier
                    .size(42.dp)
                    .padding(8.dp)
            )
        } else {
            Text(
                text = number,
                color = textColor,
                textAlign = TextAlign.Center,
                maxLines = 1,
                fontSize = fontSize.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(8.dp)
            )
        }

        if (isLocked) {
            Icon(
                imageVector = Icons.Filled.Lock,
                contentDescription = "Ikona kłódki",
                tint = Color.White,
                modifier = Modifier
                    .size(24.dp)
                    .padding(start = 6.dp)
                    .align(Alignment.BottomEnd)
            )
        }
    }
}

@Preview
@Composable
fun OrdersScreenPreview() {
    OrdersScreenContent(
        state = OrderContract.State(
            orders = listOf(
                OrderEntity(0, 1, OrderState.Deleted),
                OrderEntity(1, 2, OrderState.Ready),
                OrderEntity(2, 3, OrderState.Ready),
                OrderEntity(3, 4, OrderState.InProgress),
                OrderEntity(4, 5, OrderState.InProgress),
                OrderEntity(5, 6, OrderState.InProgress),
                OrderEntity(6, 7, OrderState.InProgress),
            )
        ),
        onAction = {}
    )
}
