package pl.lazicki.ordermanager.ui.main

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.bluetooth.domain.BluetoothDeviceDomain
import pl.lazicki.bluetooth.domain.ConnectionResult
import pl.lazicki.data.repository.ScreenSettingsRepository
import pl.lazicki.ordermanager.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val screenSettingsRepository: ScreenSettingsRepository,
    private val bluetoothController: BluetoothController
) : MviViewModel<MainContract.State, MainContract.Action, MainContract.Effect>(
    initialState = MainContract.State(isConnected = false)
) {
    private var reconnectJob: Job? = null
    private val RECONNECT_DELAY_MS = 10000L

    init {
        observeBluetoothConnection()
    }

    private fun observeBluetoothConnection() {
        viewModelScope.launch(Dispatchers.IO) {
            bluetoothController.isConnected.collect { isConnected ->
                updateState {
                    copy(
                        isConnected = isConnected,
                        isConnecting = false,
                        errorMessage = null
                    )
                }

                if (isConnected) {
                    reconnectJob?.cancel()
                    reconnectJob = null
                    pushEffect(MainContract.Effect.NavigateToOrderScreen)
                } else {
                    if (screenSettingsRepository.getLastConnectedDevice() == null) {
                        pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
                    } else if (reconnectJob == null) {
                        scheduleReconnect()
                    }
                }
            }
        }
        viewModelScope.launch(Dispatchers.IO) {
            bluetoothController.errors.collect { error ->
                updateState { copy(errorMessage = error) }
                pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
            }
        }
    }

    private fun scheduleReconnect() {
        reconnectJob?.cancel()
        reconnectJob = viewModelScope.launch(Dispatchers.IO) {
            tryReconnectToLastDevice()
        }
    }

    private fun tryReconnectToLastDevice() {
        viewModelScope.launch(Dispatchers.IO) {
            updateState { copy(isConnecting = true) }

            try {
                val lastDeviceInfo = screenSettingsRepository.getLastConnectedDevice()

                if (lastDeviceInfo != null) {
                    val lastDevice = BluetoothDeviceDomain(
                        address = lastDeviceInfo.first,
                        name = lastDeviceInfo.second
                    )
                    println("[MainViewModel] Trying to reconnect to last device: ${lastDevice.name} (${lastDevice.address})")

                    val pairedDevices = bluetoothController.pairedDevices.first()
                    val isPaired = pairedDevices.any { it.address == lastDevice.address }

                    if (isPaired) {
                        connectToDevice(lastDevice)
                    } else {
                        println("[MainViewModel] Last device is not paired anymore")
                        updateState { copy(isConnecting = false) }
                        pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
                    }
                } else {
                    println("[MainViewModel] No last connected device found")
                    updateState { copy(isConnecting = false) }
                    pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
                }
            } catch (e: Exception) {
                println("[MainViewModel] Error reconnecting to last device: ${e.message}")
                updateState { copy(isConnecting = false) }
                pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
            }
        }
    }

    private fun connectToDevice(device: BluetoothDeviceDomain) {
        viewModelScope.launch(Dispatchers.IO) {
            updateState { copy(isConnecting = true) }

            try {
                var reconnectAttempts = 0
                var connected = false
                val maxAttempts = 3  // Ograniczamy liczbę prób do 3

                while (!connected && reconnectAttempts < maxAttempts) {
                    println("[MainViewModel] Connecting to device: ${device.name} (${device.address}), attempt ${reconnectAttempts + 1}")

                    try {
                        bluetoothController.connectToDevice(device).collect { result ->
                            when (result) {
                                ConnectionResult.ConnectionEstablished -> {
                                    println("[MainViewModel] Connection established")
                                    connected = true

                                    screenSettingsRepository.saveLastConnectedDevice(
                                        deviceAddress = device.address,
                                        deviceName = device.name
                                    )

                                    updateState {
                                        copy(
                                            isConnected = true,
                                            isConnecting = false,
                                            errorMessage = null
                                        )
                                    }
                                }

                                is ConnectionResult.Error -> {
                                    println("[MainViewModel] Connection error: ${result.message}")
                                    updateState {
                                        copy(
                                            errorMessage = result.message
                                        )
                                    }
                                }

                                else -> {
                                    /** Ignorujemy inne wyniki */
                                }
                            }
                        }
                    } catch (e: Exception) {
                        println("[MainViewModel] Error in connection flow: ${e.message}")
                    }

                    if (!connected) {
                        reconnectAttempts++
                        if (reconnectAttempts < maxAttempts) {
                            delay(RECONNECT_DELAY_MS)
                        }
                    }
                }

                if (!connected) {
                    println("[MainViewModel] Failed to connect after $maxAttempts attempts")
                    updateState {
                        copy(
                            isConnected = false,
                            isConnecting = false,
                            errorMessage = "Failed to connect after $maxAttempts attempts"
                        )
                    }

                    // Jeśli nie udało się połączyć po kilku próbach, przechodzimy do ekranu powitalnego
                    pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
                }
            } catch (e: Exception) {
                println("[MainViewModel] Error connecting to device: ${e.message}")
                updateState {
                    copy(
                        isConnected = false,
                        isConnecting = false,
                        errorMessage = "Error connecting to device: ${e.message}"
                    )
                }

                // W przypadku błędu przechodzimy do ekranu powitalnego
                pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        reconnectJob?.cancel()
    }
}