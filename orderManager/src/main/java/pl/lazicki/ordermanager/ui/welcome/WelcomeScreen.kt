package pl.lazicki.ordermanager.ui.welcome

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import pl.lazicki.ordermanager.OrderManagerTheme
import pl.lazicki.ordermanager.components.BluetoothDeviceList
import pl.lazicki.ordermanager.core.mvi.EffectCollector
import pl.lazicki.ordermanager.ui.destinations.OrdersScreenDestination
import pl.lazicki.ordermanager.ui.main.MainContract
import pl.lazicki.ordermanager.ui.main.MainViewModel

@Destination
@Composable
fun WelcomeScreen(
    viewModel: WelcomeViewModel = hiltViewModel(),
    mainViewModel: MainViewModel = hiltViewModel(),
    navigator: DestinationsNavigator,
) {
    val mainState: MainContract.State by mainViewModel.state.collectAsStateWithLifecycle()
    val uiState: WelcomeContract.State by viewModel.state.collectAsStateWithLifecycle()

    EffectCollector(effect = viewModel.effect) { effect ->
        when (effect) {
            is WelcomeContract.Effect.NavigateToOrderScreen -> {
                navigator.navigate(OrdersScreenDestination)
            }
        }
    }

    WelcomeScreenContent(
        isConnecting = mainState.isConnecting,
        uiState = uiState,
        onAction = viewModel::onAction
    )
}

@Composable
fun WelcomeScreenContent(
    isConnecting: Boolean,
    uiState: WelcomeContract.State,
    onAction: (WelcomeContract.Action) -> Unit
) {
    Column(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.background)
            .fillMaxSize()
    ) {
        BluetoothDeviceList(
            isConnecting = uiState.isConnecting || isConnecting,
            isConnected = uiState.isConnected,
            errorMessage = uiState.errorMessage,
            lastConnectedDevice = uiState.lastConnectedDevice,
            pairedDevices = uiState.pairedDevices,
            scannedDevices = uiState.scannedDevices,
            onClick = { onAction(WelcomeContract.Action.OnDeviceClick(it)) },
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            horizontalArrangement = Arrangement.SpaceAround
        ) {

            val (buttonColor, action) = if (uiState.lastConnectedDevice == null) {
                if (uiState.isSearching) {
                    MaterialTheme.colorScheme.primary to WelcomeContract.Action.OnStopScan
                } else {
                    MaterialTheme.colorScheme.primary to WelcomeContract.Action.OnStartScan
                }
            } else {
                Color(0xFFFF6B6B) to WelcomeContract.Action.OnRemoveLastConnectionClick
            }

            Button(
                onClick = { onAction(action) },
                colors = ButtonDefaults.buttonColors(
                    containerColor = buttonColor,
                    contentColor = Color.White
                )
            ) {
                val text = if (uiState.lastConnectedDevice == null) {
                    if (uiState.isSearching) {
                        "Wyszukiwanie..."
                    } else {
                        "Szukaj Urządzeń"
                    }
                } else {
                    "Połącz z innym urządzeniem"
                }

                Text(
                    text = text,
                    modifier = Modifier.padding(12.dp),
                )
            }
        }
    }
}

@Preview(
    widthDp = 540,
    heightDp = 960,
    device = Devices.TV_1080p
)
@Composable
fun WelcomeScreenPreview() {
    OrderManagerTheme {
        WelcomeScreenContent(
            isConnecting = false,
            uiState = WelcomeContract.State(),
            onAction = {}
        )
    }
}