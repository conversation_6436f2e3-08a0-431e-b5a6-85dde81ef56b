package pl.lazicki.ordermanager.ui.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import pl.lazicki.design.components.PressedText
import pl.lazicki.ordermanager.core.mvi.EffectCollector
import pl.lazicki.ordermanager.ui.destinations.WelcomeScreenDestination
import pl.lazicki.ordermanager.ui.orders.OrderItem

@Destination
@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel = hiltViewModel(),
    navigator: DestinationsNavigator
) {
    val uiState: SettingsContract.State by viewModel.state.collectAsStateWithLifecycle()

    EffectCollector(effect = viewModel.effect) { effect ->
        when (effect) {
            is SettingsContract.Effect.NavigateBack -> {
                navigator.navigateUp()
            }

            SettingsContract.Effect.NavigateToBluetoothSettings -> {
                navigator.navigate(WelcomeScreenDestination.route)
            }
        }
    }

    SettingsScreenContent(uiState, viewModel::onAction)
}

@Composable
fun SettingsScreenContent(
    state: SettingsContract.State,
    onAction: (SettingsContract.Action) -> Unit
) {
    var showMaxOrderNumberDialog by remember { mutableStateOf(false) }
    var showTitleInProgressDialog by remember { mutableStateOf(false) }
    var showTitleReadyDialog by remember { mutableStateOf(false) }
    var titleInProgressText by remember { mutableStateOf(state.titleInProgress) }
    var titleReadyText by remember { mutableStateOf(state.titleReady) }

    LaunchedEffect(state.titleInProgress) {
        titleInProgressText = state.titleInProgress
    }

    LaunchedEffect(state.titleReady) {
        titleReadyText = state.titleReady
    }

    Column(
        modifier = Modifier
            .background(MaterialTheme.colorScheme.background)
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(10.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            modifier = Modifier
                .width(540.dp)
                .padding(bottom = 24.dp)
        ) {
            Text(
                text = "Ustawienia",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
            )
            Text(
                text = "Wersja aplikacji: ${state.appVersion}",
                style = MaterialTheme.typography.bodySmall,
            )
        }

        SettingItem(
            title = "Czyszczenie listy zamówień",
            content = {
                Button(
                    onClick = { onAction(SettingsContract.Action.ClearOrders) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF6B6B))
                ) {
                    Text("Usuń wszystkie zamówienia")
                }
            }
        )

        SettingItem(
            title = "Zakres numeru zamówienia",
            content = {
                Column {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp)
                    ) {
                        Text(
                            text = "Kliknij w numer żeby zmienić zakres",
                            color = Color.Black,
                            fontWeight = FontWeight.Bold,
                            fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )
                    }
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 6.dp)
                    ) {
                        OrderItem(
                            isOrdersLoading = false,
                            isLocked = true,
                            fontSize = 32,
                            number = "1",
                            modifier = Modifier.width(92.dp)
                        )
                        Text(
                            text = "-",
                            color = Color.Black,
                            fontWeight = FontWeight.Bold,
                            fontSize = MaterialTheme.typography.headlineLarge.fontSize,
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )
                        OrderItem(
                            isOrdersLoading = false,
                            isLocked = false,
                            fontSize = 32,
                            number = "${state.maxOrderNumber}",
                            modifier = Modifier
                                .width(92.dp)
                                .clickable { showMaxOrderNumberDialog = true }
                        )
                    }
                }
            }
        )

        SettingItem(
            title = "Rozmiar czcionki",
            content = {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = { onAction(SettingsContract.Action.DecreaseFontSize) },
                        modifier = Modifier.width(60.dp),
                        contentPadding = ButtonDefaults.ContentPadding
                    ) {
                        Text(
                            text = "-",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = MaterialTheme.typography.headlineSmall.fontSize,
                        )
                    }

                    Text(
                        text = "${state.fontSize}",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(horizontal = 8.dp)
                    )

                    Button(
                        onClick = { onAction(SettingsContract.Action.IncreaseFontSize) },
                        modifier = Modifier.width(60.dp),
                        contentPadding = ButtonDefaults.ContentPadding
                    ) {
                        Text(
                            text = "+",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = MaterialTheme.typography.headlineSmall.fontSize,
                        )
                    }
                }
            }
        )

        SettingItem(
            title = "Szerokość zamówienia",
            content = {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Button(
                        onClick = { onAction(SettingsContract.Action.DecreaseOrderWidth) },
                        modifier = Modifier.width(60.dp),
                        contentPadding = ButtonDefaults.ContentPadding
                    ) {
                        Text(
                            text = "-",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = MaterialTheme.typography.headlineSmall.fontSize,
                        )
                    }

                    Text(
                        text = "${state.orderWidth}",
                        style = MaterialTheme.typography.headlineMedium,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(horizontal = 8.dp)
                    )

                    Button(
                        onClick = { onAction(SettingsContract.Action.IncreaseOrderWidth) },
                        modifier = Modifier.width(60.dp),
                    ) {
                        Text(
                            text = "+",
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = MaterialTheme.typography.headlineSmall.fontSize,
                        )
                    }
                }
            }
        )

        SettingItem(
            title = "Tytuł pierwszej kolumny",
            content = {
                OutlinedButton(
                    onClick = { showTitleInProgressDialog = true },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(state.titleInProgress.uppercase())
                }
            }
        )

        SettingItem(
            title = "Tytuł drugiej kolumny",
            content = {
                OutlinedButton(
                    onClick = { showTitleReadyDialog = true },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(state.titleReady.uppercase())
                }
            }
        )

//        SettingItem(
//            title = "Obrót ekranu (aktualnie: ${state.screenOrientation}°)",
//            content = {
//                Button(
//                    onClick = { onAction(SettingsContract.Action.RotateScreen) },
//                    modifier = Modifier.fillMaxWidth()
//                ) {
//                    Text("Obróć ekran o 90°")
//                }
//            }
//        )

        SettingItem(
            title = "Reset ekranu do ustawień fabrycznych",
            content = {
                Button(
                    onClick = { onAction(SettingsContract.Action.ResetToDefault) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF6B6B))
                ) {
                    Text("Przywróć domyślne ustawienia ekranu")
                }
            }
        )

        SettingItem(
            title = "Połączenie z ekranem",
            content = {
                Button(
                    onClick = { onAction(SettingsContract.Action.OpenBluetoothSettings) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF6B6B))
                ) {
                    Text("Przejdź do wyboru urządzenia")
                }
            }
        )
    }

    if (showMaxOrderNumberDialog) {
        EditTextDialog(
            title = "Zmień maksymalny zakres numeru zamówienia.\n\nMinimalna wartość to 10 a maksymalna to 999.",
            initialValue = state.maxOrderNumber.toString(),
            onDismiss = { showMaxOrderNumberDialog = false },
            onConfirm = { changed ->
                val newMaxNumber = changed.toIntOrNull()
                if (newMaxNumber != null && newMaxNumber > 10 && newMaxNumber < 1000) {
                    onAction(SettingsContract.Action.UpdateMaxOrderNumber(newMaxNumber))
                    showMaxOrderNumberDialog = false
                }
            },
            isNumericOnly = true
        )
    }

    if (showTitleInProgressDialog) {
        EditTextDialog(
            title = "Edytuj tytuł pierwszej kolumny",
            initialValue = titleInProgressText,
            onDismiss = { showTitleInProgressDialog = false },
            onConfirm = { newTitle ->
                onAction(SettingsContract.Action.UpdateTitleInProgress(newTitle))
                showTitleInProgressDialog = false
            }
        )
    }

    if (showTitleReadyDialog) {
        EditTextDialog(
            title = "Edytuj tytuł drugiej kolumny",
            initialValue = titleReadyText,
            onDismiss = { showTitleReadyDialog = false },
            onConfirm = { newTitle ->
                onAction(SettingsContract.Action.UpdateTitleReady(newTitle))
                showTitleReadyDialog = false
            }
        )
    }
}

@Composable
fun SettingItem(
    title: String,
    content: @Composable () -> Unit
) {
    Column(
        modifier = Modifier
            .widthIn(min = 360.dp, max = 540.dp)
            .padding(vertical = 8.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface,
                contentColor = MaterialTheme.colorScheme.onSurface,
                disabledContainerColor = MaterialTheme.colorScheme.surface,
                disabledContentColor = MaterialTheme.colorScheme.onSurface
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 4.dp,
                pressedElevation = 8.dp
            )
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                content()
            }
        }
    }
}


@Composable
fun EditTextDialog(
    title: String,
    initialValue: String,
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit,
    isNumericOnly: Boolean = false,
) {
    var text by remember { mutableStateOf(initialValue) }

    androidx.compose.material3.AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
            )
        },
        text = {
            OutlinedTextField(
                value = text,
                onValueChange = { newValue ->
                    if (isNumericOnly) {
                        if (newValue.all { it.isDigit() }) {
                            text = newValue
                        }
                    } else {
                        text = newValue
                    }
                },
                singleLine = true,
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = if (isNumericOnly) {
                    KeyboardOptions(keyboardType = KeyboardType.Number)
                } else {
                    KeyboardOptions.Default
                }
            )
        },
        confirmButton = {
            TextButton(onClick = {
                if (isNumericOnly && text.isBlank() && initialValue.isNotBlank()) {
                    onConfirm(text)
                } else if (isNumericOnly && text.isBlank() && initialValue.isBlank()) {
                    onConfirm(initialValue)
                } else {
                    onConfirm(text)
                }
            }) {
                Text("Zapisz")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Anuluj")
            }
        }
    )
}

@Preview
@Composable
fun SettingsScreenContentPreview() {
    SettingsScreenContent(
        state = SettingsContract.State(appVersion = "1.0.0"),
        onAction = {}
    )
}
