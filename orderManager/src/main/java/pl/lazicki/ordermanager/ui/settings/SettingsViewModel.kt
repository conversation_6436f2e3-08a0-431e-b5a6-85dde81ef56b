package pl.lazicki.ordermanager.ui.settings

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.bluetooth.domain.Command
import pl.lazicki.bluetooth.domain.CommandType
import pl.lazicki.bluetooth.domain.Settings
import pl.lazicki.data.repository.OrderRepository
import pl.lazicki.data.repository.ScreenSettingsRepository
import pl.lazicki.ordermanager.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val bluetoothController: BluetoothController,
    private val orderRepository: OrderRepository,
    private val screenSettingsRepository: ScreenSettingsRepository,
) : MviViewModel<
        SettingsContract.State,
        SettingsContract.Action,
        SettingsContract.Effect>(
    initialState = SettingsContract.State(appVersion = "1.0.0")
) {

    init {
        loadSettings()
    }

    private fun loadSettings() {
        viewModelScope.launch(Dispatchers.IO) {
            screenSettingsRepository.observeScreenSettings().collect { screenSettings ->
                screenSettings?.let {
                    updateState {
                        copy(
                            maxOrderNumber = it.maxOrderNumber,
                            fontSize = it.fontSize,
                            orderWidth = it.orderWidth,
                            titleInProgress = it.titleInProgress,
                            titleReady = it.titleReady,
                            screenOrientation = it.screenOrientation
                        )
                    }
                }
            }
        }
    }

    override fun onAction(action: SettingsContract.Action) {
        when (action) {
            is SettingsContract.Action.IncreaseFontSize -> {
                viewModelScope.launch(Dispatchers.IO) {
                    val newSize = state.value.fontSize + 1
                    if (newSize <= 80) {
                        screenSettingsRepository.updateFontSize(newSize)
                        syncSettings()
                    }
                }
            }

            is SettingsContract.Action.DecreaseFontSize -> {
                viewModelScope.launch(Dispatchers.IO) {
                    val newSize = state.value.fontSize - 1
                    if (newSize >= 12) {
                        screenSettingsRepository.updateFontSize(newSize)
                        syncSettings()
                    }
                }
            }

            is SettingsContract.Action.IncreaseOrderWidth -> {
                viewModelScope.launch(Dispatchers.IO) {
                    val newWidth = state.value.orderWidth + 5
                    if (newWidth <= 230) {
                        screenSettingsRepository.updateOrderWidth(newWidth)
                        syncSettings()
                    }
                }
            }

            is SettingsContract.Action.DecreaseOrderWidth -> {
                viewModelScope.launch(Dispatchers.IO) {
                    val newWidth = state.value.orderWidth - 5
                    if (newWidth >= 50) {
                        screenSettingsRepository.updateOrderWidth(newWidth)
                        syncSettings()
                    }
                }
            }

            is SettingsContract.Action.UpdateTitleInProgress -> {
                viewModelScope.launch(Dispatchers.IO) {
                    updateState { copy(titleInProgress = action.title) }
                    screenSettingsRepository.updateTitleInProgress(action.title)
                    syncSettings()
                }
            }

            is SettingsContract.Action.UpdateTitleReady -> {
                viewModelScope.launch(Dispatchers.IO) {
                    updateState { copy(titleReady = action.title) }
                    screenSettingsRepository.updateTitleReady(action.title)
                    syncSettings()
                }
            }

            is SettingsContract.Action.RotateScreen -> {
                viewModelScope.launch(Dispatchers.IO) {
                    val currentOrientation = state.value.screenOrientation
                    val nextOrientation = (currentOrientation + 90) % 360
                    screenSettingsRepository.updateScreenOrientation(nextOrientation)
                    syncScreenOrientation()
                }
            }

            is SettingsContract.Action.ClearOrders -> deleteAllOrders()

            is SettingsContract.Action.ResetToDefault -> {
                viewModelScope.launch(Dispatchers.IO) {
                    screenSettingsRepository.restoreDefaultSettings()
                    val defaults = screenSettingsRepository.getDefaultScreenSettings()
                    updateState {
                        copy(
                            fontSize = defaults.fontSize,
                            maxOrderNumber = defaults.maxOrderNumber,
                            titleInProgress = defaults.titleInProgress,
                            titleReady = defaults.titleReady,
                            screenOrientation = defaults.screenOrientation,
                            orderWidth = defaults.orderWidth
                        )
                    }
                    syncSettings()
                }
            }

            SettingsContract.Action.OpenBluetoothSettings -> {
                pushEffect(SettingsContract.Effect.NavigateToBluetoothSettings)
            }

            is SettingsContract.Action.UpdateMaxOrderNumber -> {
                viewModelScope.launch(Dispatchers.IO) {
                    updateState { copy(maxOrderNumber = action.maxOrderNumber) }
                    screenSettingsRepository.updateMaxOrderNumber(action.maxOrderNumber)
                }
            }
        }
    }

    /** Ta metoda przesyła wszystkie wartości ale screenOrientation nie jest ustawiany! */
    private suspend fun syncSettings() {
        val currentSettings = state.value
        val settings = Settings(
            screenOrientation = currentSettings.screenOrientation,
            inProgressTitle = currentSettings.titleInProgress,
            readyTitle = currentSettings.titleReady,
            fontSize = currentSettings.fontSize,
            orderWidth = currentSettings.orderWidth
        )

        val success = bluetoothController.trySendCommand(
            Command(
                type = CommandType.SETTINGS,
                settings = settings
            )
        )

        println("[SettingsViewModel] syncSettings trySendCommand, success: $success")
    }

    /** Ta metoda przesyła wszystkie wartości ale pod uwage jest tylko screenOrientation */
    private suspend fun syncScreenOrientation() {
        val currentSettings = state.value
        val settings = Settings(
            screenOrientation = currentSettings.screenOrientation,
            inProgressTitle = currentSettings.titleInProgress,
            readyTitle = currentSettings.titleReady,
            fontSize = currentSettings.fontSize,
            orderWidth = currentSettings.orderWidth
        )

        val success = bluetoothController.trySendCommand(
            Command(
                type = CommandType.ROTATE_SCREEN,
                settings = settings
            )
        )
        println("[SettingsViewModel] syncScreenOrientation trySendCommand, success: $success")
    }

    private fun deleteAllOrders() {
        viewModelScope.launch(Dispatchers.IO) {
            orderRepository.deleteAllOrders()
            val success = bluetoothController.trySendCommand(
                Command(type = CommandType.DELETE_ALL_ORDERS)
            )
            println("[SettingsViewModel] deleteAllOrders sent, success: $success")
            if (success == true) {
                pushEffect(SettingsContract.Effect.NavigateBack)
            } else {
                updateState { copy(error = "Failed to delete orders") }
            }
        }
    }
}
