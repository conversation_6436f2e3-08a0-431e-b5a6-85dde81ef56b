package pl.lazicki.design.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun PressedText(
    text: String,
    modifier: Modifier = Modifier
) {
    val textMeasurer = rememberTextMeasurer()
    val textStyle = TextStyle(fontSize = 14.sp, color = Color.Black)

    Canvas(modifier = modifier
        .fillMaxSize()
        .background(Color(0xFFFF8C42)) // Orange textured background
    ) {
        // Draw background texture (simple lines for effect)
        for (i in 0..size.width.toInt() step 10) {
            drawLine(
                color = Color(0xFFE07A5F).copy(alpha = 0.3f),
                start = Offset(i.toFloat(), 0f),
                end = Offset(i.toFloat(), size.height),
                strokeWidth = 2.dp.toPx()
            )
        }

        // Draw text with embossed effect
        val textLayoutResult = textMeasurer.measure(text, textStyle)
        val centerX = (size.width - textLayoutResult.size.width) / 2
        val centerY = (size.height - textLayoutResult.size.height) / 2

        // Draw shadow for depth
        drawText(
            textLayoutResult = textLayoutResult,
            topLeft = Offset(centerX + 4.dp.toPx(), centerY + 4.dp.toPx()),
            drawStyle = Fill,
            color = Color(0xFF4A2C2A) // Dark shadow color
        )

        // Draw main text with cut-out effect
        drawText(
            textLayoutResult = textLayoutResult,
            topLeft = Offset(centerX, centerY),
            drawStyle = Fill,
            color = Color(0xFFFF8C42) // Match background for cut-out effect
        )

        // Optional: Add a slight outline for definition
        drawText(
            textLayoutResult = textLayoutResult,
            topLeft = Offset(centerX, centerY),
            drawStyle = Stroke(width = 2.dp.toPx()),
            color = Color(0xFF4A2C2A) // Dark outline
        )
    }
}