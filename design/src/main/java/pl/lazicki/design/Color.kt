package pl.lazicki.design

import androidx.compose.ui.graphics.Color

object AppColors {
    val Primary = Color(0xFF1565C0)        // Głęboki niebieski
    val OnPrimary = Color.White

    val PrimaryContainer = Color(0xFF1565C0) // Utrzymuje głęboki niebieski w light theme
    val OnPrimaryContainer = Color(0xFFE3F2FD)

    val Secondary = Color(0xFF546E7A)
    val OnSecondary = Color(0xFFFFFFFF)
    val SecondaryContainer = Color(0xFFECEFF1)
    val OnSecondaryContainer = Color(0xFF263238)

    val Tertiary = Color(0xFF616161)
    val OnTertiary = Color(0xFFFFFFFF)
    val TertiaryContainer = Color(0xFFF5F5F5)
    val OnTertiaryContainer = Color(0xFF424242)

    val Background = Color(0xFFF5F5F5)     // Jasne tło aplikacji
    val OnBackground = Color(0xFF1C1C1C)

    val Surface = Color(0xFFFFFFFF)       // Jasne tło kart
    val OnSurface = Color(0xFF1C1C1C)

    val SurfaceVariant = Color(0xFFF0F0F0)
    val OnSurfaceVariant = Color(0xFF333333)

    val Outline = Color(0xFFBDBDBD)
    val OutlineVariant = Color(0xFFE0E0E0)

    val Error = Color(0xFFD32F2F)
    val OnError = Color(0xFFFFFFFF)
    val ErrorContainer = Color(0xFFFFEBEE)
    val OnErrorContainer = Color(0xFFB71C1C)
}
