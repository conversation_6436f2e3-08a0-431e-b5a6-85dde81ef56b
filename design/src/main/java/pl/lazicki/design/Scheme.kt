package pl.lazicki.design

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.tv.material3.lightColorScheme as tvLightColorScheme
import androidx.compose.ui.graphics.Color

val ColorScheme = lightColorScheme(
    primary = AppColors.Primary,
    onPrimary = AppColors.OnPrimary,
    primaryContainer = AppColors.PrimaryContainer,
    onPrimaryContainer = AppColors.OnPrimaryContainer,

    secondary = AppColors.Secondary,
    onSecondary = AppColors.OnSecondary,
    secondaryContainer = AppColors.SecondaryContainer,
    onSecondaryContainer = AppColors.OnSecondaryContainer,

    tertiary = AppColors.Tertiary,
    onTertiary = AppColors.OnTertiary,
    tertiaryContainer = AppColors.TertiaryContainer,
    onTertiaryContainer = AppColors.OnTertiaryContainer,

    error = AppColors.Error,
    onError = AppColors.OnError,
    errorContainer = AppColors.ErrorContainer,
    onErrorContainer = AppColors.OnErrorContainer,

    background = AppColors.Background,
    onBackground = AppColors.OnBackground,
    surface = AppColors.Surface,
    onSurface = AppColors.OnSurface,
    surfaceVariant = AppColors.SurfaceVariant,
    onSurfaceVariant = AppColors.OnSurfaceVariant,

    outline = AppColors.Outline,
    outlineVariant = AppColors.OutlineVariant,

    inverseSurface = Color(0xFF2C2C2C),
    inverseOnSurface = Color(0xFFF0F0F0),
    inversePrimary = Color(0xFF90CAF9),

    surfaceTint = AppColors.Primary
)

val ColorSchemeTv = tvLightColorScheme(
    primary = AppColors.Primary,
    onPrimary = AppColors.OnPrimary,
    primaryContainer = AppColors.PrimaryContainer,
    onPrimaryContainer = AppColors.OnPrimaryContainer,

    secondary = AppColors.Secondary,
    onSecondary = AppColors.OnSecondary,
    secondaryContainer = AppColors.SecondaryContainer,
    onSecondaryContainer = AppColors.OnSecondaryContainer,

    tertiary = AppColors.Tertiary,
    onTertiary = AppColors.OnTertiary,
    tertiaryContainer = AppColors.TertiaryContainer,
    onTertiaryContainer = AppColors.OnTertiaryContainer,

    error = AppColors.Error,
    onError = AppColors.OnError,
    errorContainer = AppColors.ErrorContainer,
    onErrorContainer = AppColors.OnErrorContainer,

    background = AppColors.Background,
    onBackground = AppColors.OnBackground,
    surface = AppColors.Surface,
    onSurface = AppColors.OnSurface,
    surfaceVariant = AppColors.SurfaceVariant,
    onSurfaceVariant = AppColors.OnSurfaceVariant,

    inverseSurface = Color(0xFF2C2C2C),
    inverseOnSurface = Color(0xFFF0F0F0),
    inversePrimary = Color(0xFF90CAF9),

    surfaceTint = AppColors.Primary
)

val DarkColorScheme = darkColorScheme(
    primary = Color(0xFF90CAF9),           // Jaśniejszy niebieski w trybie ciemnym
    onPrimary = Color(0xFF0D47A1),         // Ciemny niebieski na jaśniejszym
    primaryContainer = Color(0xFF1565C0),   // Główny niebieski jako kontener
    onPrimaryContainer = Color(0xFFE3F2FD), // Jasny niebieski na kontenerze

    secondary = Color(0xFFB0BEC5),         // Jaśniejszy szary
    onSecondary = Color(0xFF263238),       // Ciemny szary na jaśniejszym
    secondaryContainer = Color(0xFF37474F), // Ciemny szary kontener
    onSecondaryContainer = Color(0xFFECEFF1), // Jasny szary na kontenerze

    tertiary = Color(0xFF9E9E9E),          // Jaśniejszy szary
    onTertiary = Color(0xFF424242),        // Ciemny szary na jaśniejszym
    tertiaryContainer = Color(0xFF757575),  // Średni szary jako kontener
    onTertiaryContainer = Color(0xFFF5F5F5), // Jasny szary na kontenerze

    error = Color(0xFFEF5350),            // Jaśniejszy czerwony
    onError = Color(0xFFB71C1C),          // Ciemny czerwony na jaśniejszym
    errorContainer = Color(0xFFD32F2F),    // Główny czerwony jako kontener
    onErrorContainer = Color(0xFFFFEBEE),  // Jasny czerwony na kontenerze

    background = Color(0xFF121212),        // Ciemne tło
    onBackground = Color(0xFFE0E0E0),      // Jasny tekst na ciemnym tle
    surface = Color(0xFF1E1E1E),          // Ciemna powierzchnia
    onSurface = Color(0xFFE0E0E0),        // Jasny tekst na ciemnej powierzchni
    surfaceVariant = Color(0xFF2C2C2C),    // Wariant ciemnej powierzchni
    onSurfaceVariant = Color(0xFFB0B0B0),  // Tekst na wariancie powierzchni

    outline = Color(0xFF616161),          // Ciemne obramowanie
    outlineVariant = Color(0xFF424242),    // Ciemniejsze obramowanie

    inverseSurface = Color(0xFFE0E0E0),
    inverseOnSurface = Color(0xFF1C1C1C),
    inversePrimary = Color(0xFF1565C0),

    surfaceTint = Color(0xFF90CAF9)
)