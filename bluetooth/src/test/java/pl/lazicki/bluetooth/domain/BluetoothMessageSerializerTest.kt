package pl.lazicki.bluetooth.domain

import org.junit.Test
import org.junit.Assert.*
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState

class BluetoothMessageSerializerTest {

    @Test
    fun `test BluetoothMessage serialization and deserialization`() {
        // Given
        val orders = listOf(
            OrderEntity(id = 1, number = "Order1", state = OrderState.InProgress),
            OrderEntity(id = 2, number = "Order2", state = OrderState.InProgress),
            OrderEntity(id = 3, number = "Order3", state = OrderState.Ready)
        )

        val command = Command(
            type = CommandType.SYNCHRONIZE,
            orders = orders
        )

        val originalMessage = BluetoothMessage(
            command = command,
            senderName = "TestDevice",
            isFromLocalUser = false
        )

        // When
        val jsonString = BluetoothMessageSerializer.toJson(originalMessage)
        val deserializedMessage = BluetoothMessageSerializer.fromJson(jsonString)

        // Then
        assertEquals(originalMessage.command.type, deserializedMessage.command.type)
        assertEquals(originalMessage.senderName, deserializedMessage.senderName)
        assertEquals(originalMessage.isFromLocalUser, deserializedMessage.isFromLocalUser)
        assertEquals(originalMessage.command.orders?.size, deserializedMessage.command.orders?.size)
        assertEquals(originalMessage.command.orders?.get(0)?.number, deserializedMessage.command.orders?.get(0)?.number)
        assertEquals(originalMessage.command.orders?.get(0)?.state, deserializedMessage.command.orders?.get(0)?.state)
    }

    @Test
    fun `test BluetoothMessage extension functions`() {
        // Given
        val command = Command(
            type = CommandType.SETTINGS,
            settings = Settings(
                screenOrientation = 1, // 1 = LANDSCAPE
                orderWidth = 72,
                fontSize = 32,
                inProgressTitle = "W trakcie",
                readyTitle = "Gotowe"
            )
        )
        
        val originalMessage = BluetoothMessage(
            command = command,
            senderName = "ManagerDevice",
            isFromLocalUser = true
        )

        // When
        val jsonString = originalMessage.toJson()
        val deserializedMessage = BluetoothMessage.fromJson(jsonString)

        // Then
        assertEquals(originalMessage.command.type, deserializedMessage.command.type)
        assertEquals(originalMessage.senderName, deserializedMessage.senderName)
        assertEquals(originalMessage.isFromLocalUser, deserializedMessage.isFromLocalUser)
        assertEquals(originalMessage.command.settings?.screenOrientation, deserializedMessage.command.settings?.screenOrientation)
        assertEquals(originalMessage.command.settings?.inProgressTitle, deserializedMessage.command.settings?.inProgressTitle)
        assertEquals(originalMessage.command.settings?.readyTitle, deserializedMessage.command.settings?.readyTitle)
    }

    @Test
    fun `test safe deserialization with invalid JSON`() {
        // Given
        val invalidJson = "{ invalid json }"

        // When
        val result = BluetoothMessageSerializer.fromJsonSafe(invalidJson)

        // Then
        assertNull(result)
    }

    @Test
    fun `test safe deserialization with valid JSON`() {
        // Given
        val command = Command(type = CommandType.SYNCHRONIZE)
        val originalMessage = BluetoothMessage(
            command = command,
            senderName = "TestDevice",
            isFromLocalUser = false
        )
        val jsonString = originalMessage.toJson()

        // When
        val result = BluetoothMessage.fromJsonSafe(jsonString)

        // Then
        assertNotNull(result)
        assertEquals(originalMessage.command.type, result?.command?.type)
        assertEquals(originalMessage.senderName, result?.senderName)
        assertEquals(originalMessage.isFromLocalUser, result?.isFromLocalUser)
    }
}
