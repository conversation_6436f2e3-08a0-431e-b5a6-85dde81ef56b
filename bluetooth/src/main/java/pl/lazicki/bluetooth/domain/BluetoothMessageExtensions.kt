package pl.lazicki.bluetooth.domain

/**
 * Extension functions for BluetoothMessage serialization.
 */

/**
 * Converts this BluetoothMessage to a JSON string.
 * 
 * @return JSON string representation of this message
 * @throws Exception if serialization fails
 */
fun BluetoothMessage.toJson(): String {
    return BluetoothMessageSerializer.toJson(this)
}

/**
 * Companion object extension for creating BluetoothMessage from JSON string.
 */
fun BluetoothMessage.Companion.fromJson(jsonString: String): BluetoothMessage {
    return BluetoothMessageSerializer.fromJson(jsonString)
}

/**
 * Companion object extension for safely creating BluetoothMessage from JSON string.
 * Returns null if deserialization fails.
 */
fun BluetoothMessage.Companion.fromJsonSafe(jsonString: String): BluetoothMessage? {
    return BluetoothMessageSerializer.fromJsonSafe(jsonString)
}
