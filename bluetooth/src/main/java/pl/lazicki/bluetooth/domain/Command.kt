package pl.lazicki.bluetooth.domain

import kotlinx.serialization.Serializable
import pl.lazicki.data.entity.OrderEntity
import java.util.UUID

@Serializable
data class Command(
    val type: CommandType,
    val orders: List<OrderEntity>? = null,
    val settings: Settings? = null,
    val messageId: String = UUID.randomUUID().toString(),
    val isAcknowledgement: Boolean = false,
    val acknowledgedMessageId: String? = null
)

@Serializable
enum class CommandType {
    ROTATE_SCREEN,
    DELETE_ALL_ORDERS,
    SYNCHRONIZE,
    SETTINGS,
    ACK,
}

@Serializable
data class Settings(
    val screenOrientation: Int,
    val orderWidth: Int,
    val fontSize: Int,
    val inProgressTitle: String,
    val readyTitle: String,
)
