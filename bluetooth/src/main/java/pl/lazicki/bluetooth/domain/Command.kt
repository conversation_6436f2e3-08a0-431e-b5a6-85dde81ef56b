package pl.lazicki.bluetooth.domain

import kotlinx.serialization.Serializable

@Serializable
data class Command(
    val type: CommandType,
    val orders: Orders? = null,
    val settings: Settings? = null,
)

@Serializable
enum class CommandType {
    SYNCHRONIZE,
    SETTINGS,
}

@Serializable
data class Orders(
    val inProgress: List<String>,
    val ready: List<String>,
)

@Serializable
data class Settings(
    val screenOrientation: Orientation,
    val inProgressTitle: String,
    val readyTitle: String,
)

@Serializable
enum class Orientation {
    PORTRAIT,
    LANDSCAPE,
}