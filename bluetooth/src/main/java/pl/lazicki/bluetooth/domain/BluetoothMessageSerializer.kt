package pl.lazicki.bluetooth.domain

import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

/**
 * Utility class for serializing and deserializing BluetoothMessage objects to/from JSON strings.
 */
object BluetoothMessageSerializer {
    
    private val json = Json {
        prettyPrint = false
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    /**
     * Serializes a BluetoothMessage object to a JSON string.
     * 
     * @param message The BluetoothMessage to serialize
     * @return JSON string representation of the message
     * @throws Exception if serialization fails
     */
    fun toJson(message: BluetoothMessage): String {
        return json.encodeToString(message)
    }
    
    /**
     * Deserializes a JSON string to a BluetoothMessage object.
     * 
     * @param jsonString The JSON string to deserialize
     * @return BluetoothMessage object
     * @throws Exception if deserialization fails or JSON is invalid
     */
    fun fromJson(jsonString: String): BluetoothMessage {
        return json.decodeFromString(jsonString)
    }
    
    /**
     * Safely deserializes a JSON string to a BluetoothMessage object.
     * Returns null if deserialization fails.
     * 
     * @param jsonString The JSON string to deserialize
     * @return BluetoothMessage object or null if deserialization fails
     */
    fun fromJsonSafe(jsonString: String): BluetoothMessage? {
        return try {
            json.decodeFromString(jsonString)
        } catch (e: Exception) {
            null
        }
    }
}
