package pl.lazicki.bluetooth.domain

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import pl.lazicki.data.entity.OrderEntity
import javax.inject.Singleton

@Singleton
interface BluetoothController {
    val isConnected: StateFlow<Boolean>
    val scannedDevices: StateFlow<List<BluetoothDeviceDomain>>
    val pairedDevices: StateFlow<List<BluetoothDeviceDomain>>
    val errors: SharedFlow<String>
    val currentConnectedDevice: BluetoothDeviceDomain?

    fun startDiscovery()
    fun stopDiscovery()
    fun makeDiscoverable()

    fun startBluetoothServer(): Flow<ConnectionResult>
    fun connectToDevice(device: BluetoothDeviceDomain): Flow<ConnectionResult>

    suspend fun trySendCommand(command: Command): Boolean?

    fun closeConnection()
    fun release()

    fun addUiChange(order: OrderEntity)
}