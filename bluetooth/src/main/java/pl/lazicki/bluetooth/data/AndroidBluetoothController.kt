package pl.lazicki.bluetooth.data

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothServerSocket
import android.bluetooth.BluetoothSocket
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.os.Build
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.bluetooth.domain.BluetoothDeviceDomain
import pl.lazicki.bluetooth.domain.BluetoothMessage
import pl.lazicki.bluetooth.domain.Command
import pl.lazicki.bluetooth.domain.CommandType
import pl.lazicki.bluetooth.domain.ConnectionResult
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState
import java.io.IOException
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
@SuppressLint("MissingPermission")
class AndroidBluetoothController @Inject constructor(
    private val context: Context
): BluetoothController {

    private val bluetoothManager by lazy {
        context.getSystemService(BluetoothManager::class.java)
    }
    private val bluetoothAdapter by lazy {
        bluetoothManager?.adapter
    }

    override var currentConnectedDevice: BluetoothDeviceDomain? = null
    private var dataTransferService: BluetoothDataTransferService? = null
    private val messageBuffer = BluetoothMessageBuffer(::trySendCommandInternal)

    private val _isConnected = MutableStateFlow(false)
    override val isConnected: StateFlow<Boolean>
        get() = _isConnected.asStateFlow()

    private val _scannedDevices = MutableStateFlow<List<BluetoothDeviceDomain>>(emptyList())
    override val scannedDevices: StateFlow<List<BluetoothDeviceDomain>>
        get() = _scannedDevices.asStateFlow()

    private val _pairedDevices = MutableStateFlow<List<BluetoothDeviceDomain>>(emptyList())
    override val pairedDevices: StateFlow<List<BluetoothDeviceDomain>>
        get() = _pairedDevices.asStateFlow()

    private val _errors = MutableSharedFlow<String>()
    override val errors: SharedFlow<String>
        get() = _errors.asSharedFlow()

    private val foundDeviceReceiver = FoundDeviceReceiver { device ->
        _scannedDevices.update { devices ->
            val newDevice = device.toBluetoothDeviceDomain()
            if(newDevice in devices) devices else devices + newDevice
        }
    }

    private val bluetoothStateReceiver = BluetoothStateReceiver { isConnected, bluetoothDevice ->
        println("[bluetoothController] bluetoothStateReceiver isConnected: $isConnected, bluetoothDevice: ${bluetoothDevice.name}, bondState: ${bluetoothDevice.bondState}")
        if(bluetoothAdapter?.bondedDevices?.contains(bluetoothDevice) == true) {
            _isConnected.update { isConnected }
        } else {
            CoroutineScope(Dispatchers.IO).launch {
                _errors.emit("Can't connect to a non-paired device.")
            }
        }
    }

    private var currentServerSocket: BluetoothServerSocket? = null
    private var currentClientSocket: BluetoothSocket? = null
    private var reconnectJob: Job? = null

    // Dodajemy blokadę, aby zapobiec równoczesnym próbom połączenia
    private val connectionLock = Any()
    private var isConnecting = false

    init {
        updatePairedDevices()
        context.registerReceiver(
            bluetoothStateReceiver,
            IntentFilter().apply {
                addAction(BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED)
                addAction(BluetoothDevice.ACTION_ACL_CONNECTED)
                addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED)
            }
        )
    }

    override fun makeDiscoverable() {
        val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            hasPermission(Manifest.permission.BLUETOOTH_ADVERTISE)
        } else {
            hasPermission(Manifest.permission.BLUETOOTH_ADMIN)
        }

        if (!hasPermission) {
            CoroutineScope(Dispatchers.IO).launch {
                _errors.emit("No BLUETOOTH_ADVERTISE permission")
            }
            return
        }

        val discoverableIntent = Intent(BluetoothAdapter.ACTION_REQUEST_DISCOVERABLE)
        discoverableIntent.putExtra(BluetoothAdapter.EXTRA_DISCOVERABLE_DURATION, 240)
        discoverableIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(discoverableIntent)
        println("[bluetoothController] makeDiscoverable")
    }

    override fun startDiscovery() {
        val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            hasPermission(Manifest.permission.BLUETOOTH_SCAN)
        } else {
            hasPermission(Manifest.permission.BLUETOOTH) &&
            hasPermission(Manifest.permission.ACCESS_COARSE_LOCATION)
        }

        if (!hasPermission) {
            return
        }

        context.registerReceiver(
            foundDeviceReceiver,
            IntentFilter(BluetoothDevice.ACTION_FOUND)
        )

        updatePairedDevices()

        bluetoothAdapter?.startDiscovery()
        println("[bluetoothController] startDiscovery")
    }

    override fun stopDiscovery() {
        val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            hasPermission(Manifest.permission.BLUETOOTH_SCAN)
        } else {
            hasPermission(Manifest.permission.BLUETOOTH)
        }

        if (!hasPermission) {
            return
        }

        bluetoothAdapter?.cancelDiscovery()
        println("[bluetoothController] stopDiscovery")
    }

    override fun startBluetoothServer(): Flow<ConnectionResult> {
        println("[bluetoothController] startBluetoothServer")
        
        return flow {
            // Sprawdzamy i ustawiamy flagę poza blokiem synchronized
            var canProceed = false
            var serverSocket: BluetoothServerSocket? = null
            
            synchronized(connectionLock) {
                if (isConnecting) {
                    println("[BluetoothController] Connection attempt already in progress, skipping server start")
                    canProceed = false
                } else {
                    isConnecting = true
                    canProceed = true
                    
                    // Zamykamy istniejące połączenia przed utworzeniem nowego serwera
                    closeConnectionInternal()
                }
            }
            
            if (!canProceed) {
                emit(ConnectionResult.Error("Connection attempt already in progress"))
                return@flow
            }
            
            try {
                val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    hasPermission(Manifest.permission.BLUETOOTH_CONNECT)
                } else {
                    hasPermission(Manifest.permission.BLUETOOTH)
                }

                if (!hasPermission) {
                    throw SecurityException(
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
                            "No BLUETOOTH_CONNECT permission"
                        else
                            "No BLUETOOTH permission"
                    )
                }

                // Dodajemy opóźnienie przed utworzeniem serwera
                delay(500)
                
                serverSocket = bluetoothAdapter?.listenUsingRfcommWithServiceRecord(
                    "kolejkaSystem_service",
                    UUID.fromString(SERVICE_UUID)
                )
                
                synchronized(connectionLock) {
                    currentServerSocket = serverSocket
                }
                
                println("[BluetoothController] Server socket created, waiting for connections")

                try {
                    println("[BluetoothController] Waiting for client connection...")
                    val clientSocket = serverSocket?.accept()
                    
                    // Sprawdzamy, czy połączenie zostało nawiązane
                    if (clientSocket != null) {
                        // Pobieramy informacje o podłączonym urządzeniu
                        val device = clientSocket.remoteDevice
                        var connectedDevice: BluetoothDeviceDomain? = null
                        
                        if (device != null) {
                            connectedDevice = device.toBluetoothDeviceDomain()
                            println("[BluetoothController] Connected to device: ${connectedDevice.name} (${connectedDevice.address})")
                        }
                        
                        // Aktualizujemy stan po udanym połączeniu
                        synchronized(connectionLock) {
                            currentClientSocket = clientSocket
                            currentConnectedDevice = connectedDevice
                            
                            // Zamykamy serwer po nawiązaniu połączenia, aby uniknąć konfliktów
                            currentServerSocket?.close()
                            currentServerSocket = null
                            
                            dataTransferService = BluetoothDataTransferService(clientSocket)
                        }
                        
                        emit(ConnectionResult.ConnectionEstablished)
                        
                        // Aktualizujemy stan połączenia
                        _isConnected.update { true }
                        
                        val service = dataTransferService
                        if (service != null) {
                            try {
                                emitAll(
                                    service
                                        .listenForIncomingMessages()
                                        .map {
                                            ConnectionResult.TransferSucceeded(it)
                                        }
                                )
                            } catch (e: Exception) {
                                println("[BluetoothController] Error during message listening: ${e.message}")
                                emit(ConnectionResult.Error("Error during message listening: ${e.message}"))
                                throw e  // Rzucamy wyjątek, aby przejść do bloku catch
                            }
                        }
                    } else {
                        emit(ConnectionResult.Error("Failed to accept connection"))
                    }
                } catch (e: IOException) {
                    println("[BluetoothController] Error accepting connection: ${e.message}")
                    emit(ConnectionResult.Error("Failed to accept connection: ${e.message}"))
                    throw e  // Rzucamy wyjątek, aby przejść do bloku catch
                }
            } finally {
                synchronized(connectionLock) {
                    isConnecting = false
                }
            }
        }.catch { error ->
            println("[BluetoothController] Server error: ${error.message}")
            emit(ConnectionResult.Error("Server error: ${error.message}"))
            
            // Zamykamy połączenie całkowicie
            closeConnection()
        }.onCompletion {
            println("[BluetoothController] Server flow completed")
            // Zamykamy połączenie całkowicie
            closeConnection()
        }.flowOn(Dispatchers.IO)
    }

    override fun connectToDevice(device: BluetoothDeviceDomain): Flow<ConnectionResult> {
        return flow {
            var canProceed = false
            var socketToConnect: BluetoothSocket? = null

            synchronized(connectionLock) {
                if (isConnecting) {
                    println("[BluetoothController] Connection attempt already in progress, skipping")
                    canProceed = false
                } else {
                    isConnecting = true
                    canProceed = true
                    closeConnectionInternal()
                }
            }

            if (!canProceed) {
                emit(ConnectionResult.Error("Connection attempt already in progress"))
                return@flow
            }

            try {
                val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    hasPermission(Manifest.permission.BLUETOOTH_CONNECT)
                } else {
                    hasPermission(Manifest.permission.BLUETOOTH)
                }

                if (!hasPermission) {
                    throw SecurityException(
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)
                            "No BLUETOOTH_CONNECT permission"
                        else
                            "No BLUETOOTH permission"
                    )
                }

                val bluetoothDevice = bluetoothAdapter?.getRemoteDevice(device.address)
                    ?: throw IllegalArgumentException("Device not found with address ${device.address}")

                // 🔍 Sprawdź, czy urządzenie jest sparowane
                val isPaired = bluetoothAdapter?.bondedDevices?.any { it.address == device.address } == true

                if (!isPaired) {
                    emit(ConnectionResult.Error("Device is not paired. Attempting to pair..."))

                    val paired = pairDevice(bluetoothDevice)
                    if (!paired) {
                        emit(ConnectionResult.Error("Pairing failed"))
                        return@flow
                    }

                    emit(ConnectionResult.Error("Pairing succeeded, continuing connection..."))

                    // Poczekaj chwilę, by system zakończył proces parowania
                    delay(1000)
                }

                bluetoothAdapter?.cancelDiscovery()
                delay(500)

                socketToConnect = bluetoothDevice
                    .createRfcommSocketToServiceRecord(UUID.fromString(SERVICE_UUID))

                try {
                    println("[BluetoothController] Attempting to connect to ${device.name} (${device.address})")
                    socketToConnect.connect()
                    delay(1000)
                    println("[BluetoothController] Connection established with ${device.name}")

                    synchronized(connectionLock) {
                        currentClientSocket = socketToConnect
                        currentConnectedDevice = device
                        dataTransferService = BluetoothDataTransferService(socketToConnect)
                    }

                    emit(ConnectionResult.ConnectionEstablished)
                    delay(500)
                    _isConnected.update { true }

                    dataTransferService?.let { service ->
                        try {
                            emitAll(
                                service.listenForIncomingMessages()
                                    .map { ConnectionResult.TransferSucceeded(it) }
                            )
                        } catch (e: Exception) {
                            println("[BluetoothController] Error during message listening: ${e.message}")
                            emit(ConnectionResult.Error("Error during message listening: ${e.message}"))
                            throw e
                        }
                    }

                } catch (e: IOException) {
                    println("[BluetoothController] Connection error: ${e.message}")
                    socketToConnect?.close()
                    emit(ConnectionResult.Error("Connection was interrupted"))
                    throw e
                }
            } finally {
                synchronized(connectionLock) {
                    isConnecting = false
                }
            }
        }.catch { error ->
            println("[BluetoothController] Connection error: ${error.message}")
            closeConnection()
        }.onCompletion {
            println("[BluetoothController] Connection flow completed")
        }.flowOn(Dispatchers.IO)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun pairDevice(device: BluetoothDevice): Boolean = suspendCancellableCoroutine { cont ->
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                val action = intent?.action
                val bondedDevice = intent?.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)

                if (action == BluetoothDevice.ACTION_BOND_STATE_CHANGED && bondedDevice?.address == device.address) {
                    val bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.BOND_NONE)

                    if (bondState == BluetoothDevice.BOND_BONDED) {
                        context?.unregisterReceiver(this)
                        println("[BluetoothController] Pairing succeeded")
                        cont.resume(true) {}
                    } else if (bondState == BluetoothDevice.BOND_NONE) {
                        context?.unregisterReceiver(this)
                        println("[BluetoothController] Pairing failed")
                        cont.resume(false) {}
                    }
                }
            }
        }

        context.registerReceiver(
            receiver,
            IntentFilter(BluetoothDevice.ACTION_BOND_STATE_CHANGED)
        )

        val success = device.createBond()
        if (!success) {
            context.unregisterReceiver(receiver)
            cont.resume(false) {}
        }
    }

    override suspend fun trySendCommand(command: Command): Boolean? {
        // Jeśli to komenda synchronizacji z zamówieniami, używamy bufora
        if (command.type == CommandType.SYNCHRONIZE && command.orders != null) {
            messageBuffer.addOrders(command.orders)
            return true // Zwracamy true, ponieważ bufor zajmie się wysyłaniem
        } else {
            // Dla innych typów komend, wysyłamy bezpośrednio
            return trySendCommandInternal(command)
        }
    }

    private suspend fun trySendCommandInternal(command: Command): Boolean? {
        val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            hasPermission(Manifest.permission.BLUETOOTH_CONNECT)
        } else {
            hasPermission(Manifest.permission.BLUETOOTH)
        }

        if (!hasPermission) {
            return null
        }

        if (dataTransferService == null) {
            return null
        }

        val bluetoothMessage = BluetoothMessage(
            command = command,
            senderName = bluetoothAdapter?.name ?: "Unknown name",
            isFromLocalUser = true
        )

        return dataTransferService?.sendMessage(bluetoothMessage.toByteArray())
    }

    override fun closeConnection() {
        synchronized(connectionLock) {
            closeConnectionInternal()
        }
    }

    // Wewnętrzna metoda do zamykania połączenia, wywoływana wewnątrz bloku synchronized
    private fun closeConnectionInternal() {
        println("[BluetoothController] Closing connection manually")
        
        try {
            currentClientSocket?.close()
        } catch (e: IOException) {
            println("[BluetoothController] Error closing client socket: ${e.message}")
        }
        
        try {
            currentServerSocket?.close()
        } catch (e: IOException) {
            println("[BluetoothController] Error closing server socket: ${e.message}")
        }
        
        currentClientSocket = null
        currentServerSocket = null
        dataTransferService = null
        currentConnectedDevice = null

        // Aktualizujemy stan połączenia
        _isConnected.update { false }
    }

    private fun resetConnection() {
        println("[BluetoothController] Resetting connection")
        currentClientSocket?.close()
        currentClientSocket = null
        dataTransferService = null

        _isConnected.update { false }
    }

    private fun scheduleReconnect() {
        reconnectJob?.cancel()
        reconnectJob = CoroutineScope(Dispatchers.IO).launch {
            delay(5000)
            println("[BluetoothController] Attempting to reconnect...")
            startBluetoothServer().launchIn(this)
        }
    }

    override fun release() {
        reconnectJob?.cancel()
        context.unregisterReceiver(foundDeviceReceiver)
        context.unregisterReceiver(bluetoothStateReceiver)
        closeConnection()
    }

    private fun updatePairedDevices() {
        val hasPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            hasPermission(Manifest.permission.BLUETOOTH_CONNECT)
        } else {
            hasPermission(Manifest.permission.BLUETOOTH)
        }

        if (!hasPermission) {
            return
        }
        bluetoothAdapter
            ?.bondedDevices
            ?.map { it.toBluetoothDeviceDomain() }
            ?.also { devices ->
                _pairedDevices.update { devices }
            }
    }

    private fun hasPermission(permission: String): Boolean {
        return context.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED
    }

    override fun addUiChange(order: OrderEntity) {
        messageBuffer.addUiChange(order)
    }

    companion object {
        const val SERVICE_UUID = "27b7d1da-08c7-4505-a6d1-2459987e5e2d"
    }
}