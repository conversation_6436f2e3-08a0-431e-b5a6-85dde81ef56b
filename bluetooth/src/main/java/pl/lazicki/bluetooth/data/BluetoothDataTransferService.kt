package pl.lazicki.bluetooth.data

import android.bluetooth.BluetoothSocket
import pl.lazicki.bluetooth.domain.BluetoothMessage
import pl.lazicki.bluetooth.domain.TransferFailedException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import java.io.IOException

class BluetoothDataTransferService(
    private val socket: BluetoothSocket
) {
    // Zwiększamy rozmiar bufora dla większych wiadomości
    private val BUFFER_SIZE = 16384 // 16KB

    fun listenForIncomingMessages(): Flow<BluetoothMessage> {
        return flow {
            if (!socket.isConnected) {
                return@flow
            }
            val buffer = ByteArray(BUFFER_SIZE)

            while (true) {
                val byteCount = try {
                    // Sprawdzamy, czy socket jest nadal połączony przed próbą odczytu
                    if (!socket.isConnected) {
                        println("[BluetoothDataTransferService] Socket disconnected, stopping listener")
                        break
                    }

                    socket.inputStream.read(buffer)
                } catch (e: IOException) {
                    println("[BluetoothDataTransferService] IO error during read: ${e.message}")
                    throw TransferFailedException()
                }

                if (byteCount <= 0) continue

                val message = buffer.decodeToString(
                    endIndex = byteCount
                ).toBluetoothMessage(
                    isFromLocalUser = false
                )

                message?.let { emit(it) }
            }
        }.flowOn(Dispatchers.IO)
    }

    suspend fun sendMessage(bytes: ByteArray): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // Sprawdzamy, czy socket jest nadal połączony przed próbą zapisu
                if (!socket.isConnected) {
                    println("[BluetoothDataTransferService] Socket disconnected, cannot send message")
                    return@withContext false
                }

                // Dodajemy mechanizm podziału dużych wiadomości
                if (bytes.size > BUFFER_SIZE) {
                    // Dla dużych wiadomości, dzielimy je na mniejsze części
                    var offset = 0
                    while (offset < bytes.size) {
                        val chunkSize = minOf(BUFFER_SIZE, bytes.size - offset)
                        socket.outputStream.write(bytes, offset, chunkSize)
                        socket.outputStream.flush() // Upewniamy się, że dane są wysłane
                        offset += chunkSize
                        delay(100) // Zwiększamy opóźnienie między fragmentami
                    }
                } else {
                    socket.outputStream.write(bytes)
                    socket.outputStream.flush()
                }
            } catch (e: IOException) {
                println("[BluetoothDataTransferService] Error sending message: ${e.message}")
                e.printStackTrace()
                return@withContext false
            }

            true
        }
    }
}