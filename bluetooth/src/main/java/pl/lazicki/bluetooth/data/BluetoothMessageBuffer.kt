package pl.lazicki.bluetooth.data

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import pl.lazicki.bluetooth.domain.Command
import pl.lazicki.bluetooth.domain.CommandType
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState
import kotlin.math.min

class BluetoothMessageBuffer(
    private val sendFunction: suspend (Command) -> Boolean?
) {
    // Używamy Dispatchers.IO dla operacji I/O
    private val bufferScope = CoroutineScope(Dispatchers.IO)
    
    // Osobny dispatcher dla równoległego przetwarzania paczek
    private val processingDispatcher = Dispatchers.IO.limitedParallelism(4) // Ograniczamy do 4 równoległych wątków
    
    private val ordersBuffer = MutableStateFlow<List<OrderEntity>>(emptyList())
    private var isProcessing = false

    // Maksymalna liczba zamówień w jednej paczce
    private val MAX_ORDERS_PER_BATCH = 5

    // Maksymalna liczba prób wysłania
    private val MAX_RETRY_ATTEMPTS = 3

    // Opóźnienie między paczkami
    private val DELAY_BETWEEN_BATCHES = 500L
    
    // Maksymalna liczba równoległych paczek
    private val MAX_PARALLEL_BATCHES = 3

    // Przechowujemy czas dodania/modyfikacji zamówienia do bufora
    private val orderTimestamps = mutableMapOf<Int, Long>()
    
    // Przechowujemy ostatnio wysłany stan dla każdego zamówienia
    private val lastSentOrderStates = mutableMapOf<Int, OrderState>()
    
    // Licznik pełnych synchronizacji
    private var fullSyncCounter = 0
    private val FULL_SYNC_INTERVAL = 5 // Co ile cykli wysyłać pełną synchronizację
    
    // Bufor kołowy dla zmian UI
    private val uiChangeBuffer = UiChangeBuffer()
    
    // Mutex do synchronizacji dostępu do bufora
    private val bufferMutex = Mutex()
    
    // Mutex do synchronizacji dostępu do lastSentOrderStates
    private val statesMutex = Mutex()

    /**
     * Dodaje zamówienia do bufora głównego (używane głównie dla pełnej synchronizacji)
     */
    fun addOrders(orders: List<OrderEntity>) {
        println("[BluetoothMessageBuffer] Adding ${orders.size} orders to main buffer")
        
        // Zapisujemy aktualny czas dla każdego nowego/zmienionego zamówienia
        val currentTime = System.currentTimeMillis()
        bufferScope.launch {
            statesMutex.withLock {
                orders.forEach { order ->
                    // Sprawdzamy, czy zamówienie zmieniło stan od ostatniego wysłania
                    val lastState = lastSentOrderStates[order.number]
                    if (lastState == null || lastState != order.state) {
                        // Aktualizujemy timestamp tylko jeśli zamówienie jest nowe lub zmieniło stan
                        orderTimestamps[order.number] = currentTime
                    }
                }
            }
        }
        
        bufferScope.launch {
            bufferMutex.withLock {
                ordersBuffer.update { currentOrders ->
                    // Łączymy nowe zamówienia z istniejącymi, zastępując duplikaty
                    val updatedOrders = currentOrders.toMutableList()
                    
                    orders.forEach { newOrder ->
                        val existingIndex = updatedOrders.indexOfFirst { it.number == newOrder.number }
                        if (existingIndex >= 0) {
                            updatedOrders[existingIndex] = newOrder
                        } else {
                            updatedOrders.add(newOrder)
                        }
                    }
                    
                    updatedOrders
                }
            }
        }
        
        if (!isProcessing) {
            processBuffer()
        }
    }
    
    /**
     * Dodaje pojedyncze zamówienie do bufora kołowego (używane dla zmian UI)
     * Ta metoda jest wywoływana, gdy użytkownik zmienia stan zamówienia w interfejsie
     */
    fun addUiChange(order: OrderEntity) {
        println("[BluetoothMessageBuffer] Adding UI change for order #${order.number}, state: ${order.state}")
        
        // Dodajemy do bufora kołowego UI
        bufferScope.launch(Dispatchers.IO) {
            uiChangeBuffer.addOrder(order)
            
            // Dodajemy również do głównego bufora
            bufferMutex.withLock {
                ordersBuffer.update { currentOrders ->
                    val updatedOrders = currentOrders.toMutableList()
                    val existingIndex = updatedOrders.indexOfFirst { it.number == order.number }
                    
                    if (existingIndex >= 0) {
                        updatedOrders[existingIndex] = order
                    } else {
                        updatedOrders.add(order)
                    }
                    
                    updatedOrders
                }
            }
        }
        
        if (!isProcessing) {
            processBuffer()
        }
    }
    
    private fun processBuffer() {
        isProcessing = true
        
        bufferScope.launch {
            while (true) {
                // Czekamy na akumulację wiadomości przez krótki czas
                delay(300)
                
                // Najpierw przetwarzamy zmiany UI, które mają najwyższy priorytet
                val uiChanges = uiChangeBuffer.getOrders(MAX_ORDERS_PER_BATCH)
                
                if (uiChanges.isNotEmpty()) {
                    println("[BluetoothMessageBuffer] Processing UI changes buffer with ${uiChanges.size} orders")
                    
                    // Wysyłamy zmiany UI jako priorytetowe
                    val success = sendOrderBatch(uiChanges)
                    
                    if (success) {
                        println("[BluetoothMessageBuffer] Successfully sent ${uiChanges.size} UI changes")
                        
                        // Aktualizujemy informacje o ostatnio wysłanym stanie
                        statesMutex.withLock {
                            uiChanges.forEach { order ->
                                lastSentOrderStates[order.number] = order.state
                            }
                        }
                    } else {
                        println("[BluetoothMessageBuffer] Failed to send UI changes, will retry later")
                        // Dodajemy z powrotem do bufora kołowego, jeśli wysyłanie nie powiodło się
                        uiChangeBuffer.returnOrders(uiChanges)
                    }
                    
                    // Krótkie opóźnienie po wysłaniu zmian UI
                    delay(DELAY_BETWEEN_BATCHES)
                }
                
                // Następnie przetwarzamy główny bufor
                val currentOrders = bufferMutex.withLock { ordersBuffer.value }
                if (currentOrders.isNotEmpty()) {
                    println("[BluetoothMessageBuffer] Processing main buffer with ${currentOrders.size} orders")

                    // Sprawdzamy, czy należy wykonać pełną synchronizację
                    val shouldDoFullSync = fullSyncCounter % FULL_SYNC_INTERVAL == 0
                    fullSyncCounter++
                    
                    // Filtrujemy zamówienia do wysłania
                    val ordersToSend = if (shouldDoFullSync) {
                        println("[BluetoothMessageBuffer] Performing full synchronization")
                        currentOrders
                    } else {
                        // Wybieramy tylko zamówienia, które zmieniły stan lub są nowe
                        statesMutex.withLock {
                            currentOrders.filter { order ->
                                val lastState = lastSentOrderStates[order.number]
                                lastState == null || lastState != order.state
                            }
                        }
                    }
                    
                    if (ordersToSend.isEmpty()) {
                        println("[BluetoothMessageBuffer] No orders need to be sent from main buffer")
                    } else {
                        println("[BluetoothMessageBuffer] Sending ${ordersToSend.size} orders (${if (shouldDoFullSync) "full sync" else "delta sync"})")

                        // Sortujemy zamówienia według czasu dodania/modyfikacji (od najnowszych do najstarszych)
                        val sortedOrders = ordersToSend.sortedByDescending { 
                            orderTimestamps[it.number] ?: 0L 
                        }
                        
                        // Dzielimy zamówienia na mniejsze paczki
                        val batches = sortedOrders.chunked(MAX_ORDERS_PER_BATCH)
                        println("[BluetoothMessageBuffer] Splitting into ${batches.size} batches")

                        // Lista zamówień, które zostały pomyślnie wysłane
                        val successfullySentOrders = mutableListOf<OrderEntity>()
                        val successMutex = Mutex() // Mutex do synchronizacji dostępu do listy pomyślnie wysłanych zamówień

                        // Równoległe przetwarzanie paczek
                        processBatchesInParallel(batches, successfullySentOrders, successMutex)

                        // Jeśli to była pełna synchronizacja, możemy usunąć wszystkie zamówienia z bufora
                        if (shouldDoFullSync && successfullySentOrders.isNotEmpty()) {
                            println("[BluetoothMessageBuffer] Full sync completed, clearing buffer")
                            bufferMutex.withLock {
                                ordersBuffer.update { emptyList() }
                            }
                        } else if (successfullySentOrders.isNotEmpty()) {
                            // W przypadku częściowej synchronizacji, usuwamy tylko te zamówienia, 
                            // które zostały pomyślnie wysłane
                            println("[BluetoothMessageBuffer] Successfully sent ${successfullySentOrders.size} orders")
                            bufferMutex.withLock {
                                ordersBuffer.update { currentList ->
                                    currentList.filter { order ->
                                        !successfullySentOrders.any { it.number == order.number }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // Jeśli oba bufory są puste, kończymy przetwarzanie
                if (await { uiChangeBuffer.isEmpty() } && bufferMutex.withLock { ordersBuffer.value.isEmpty() }) {
                    break
                }
                
                // Jeśli nadal są zamówienia w buforach, czekamy przed ponowną próbą
                delay(1000)
            }
            
            println("[BluetoothMessageBuffer] Buffer processing completed")
            isProcessing = false
        }
    }
    
    /**
     * Przetwarza paczki równolegle, z ograniczeniem liczby równoczesnych paczek
     */
    private suspend fun processBatchesInParallel(
        batches: List<List<OrderEntity>>,
        successfullySentOrders: MutableList<OrderEntity>,
        successMutex: Mutex
    ) {
        // Przetwarzamy paczki w grupach po MAX_PARALLEL_BATCHES
        val totalBatches = batches.size
        var processedBatches = 0
        
        while (processedBatches < totalBatches) {
            val batchesToProcess = min(MAX_PARALLEL_BATCHES, totalBatches - processedBatches)
            val currentBatches = batches.subList(processedBatches, processedBatches + batchesToProcess)
            
            println("[BluetoothMessageBuffer] Processing ${currentBatches.size} batches in parallel (${processedBatches+1}-${processedBatches+batchesToProcess}/${totalBatches})")
            
            // Uruchamiamy przetwarzanie paczek równolegle
            val deferreds = currentBatches.mapIndexed { index, batch ->
                CoroutineScope(processingDispatcher).async {
                    val batchIndex = processedBatches + index
                    val success = sendOrderBatch(batch, batchIndex, totalBatches)
                    
                    if (success) {
                        // Aktualizujemy informacje o ostatnio wysłanym stanie
                        statesMutex.withLock {
                            batch.forEach { order ->
                                lastSentOrderStates[order.number] = order.state
                            }
                        }
                        
                        // Dodajemy do listy pomyślnie wysłanych zamówień
                        successMutex.withLock {
                            successfullySentOrders.addAll(batch)
                        }
                    }
                    
                    success
                }
            }
            
            // Czekamy na zakończenie wszystkich zadań
            deferreds.awaitAll()
            
            // Aktualizujemy licznik przetworzonych paczek
            processedBatches += batchesToProcess
            
            // Krótkie opóźnienie między grupami paczek
            if (processedBatches < totalBatches) {
                delay(DELAY_BETWEEN_BATCHES)
            }
        }
    }
    
    /**
     * Wysyła paczkę zamówień i zwraca informację o sukcesie
     */
    private suspend fun sendOrderBatch(
        batch: List<OrderEntity>, 
        batchIndex: Int = 0, 
        totalBatches: Int = 1
    ): Boolean {
        if (batch.isEmpty()) return true
        
        var success = false
        var attempts = 0
        
        println("[BluetoothMessageBuffer] Sending batch ${batchIndex+1}/${totalBatches} with ${batch.size} orders")
        
        // Wypisujemy numery zamówień i stany w tej paczce
        val orderDetails = batch.map { "${it.number}(${it.state})" }.joinToString(", ")
        println("[BluetoothMessageBuffer] Batch ${batchIndex+1} contains orders: $orderDetails")
        
        // Próbujemy wysłać paczkę kilka razy
        while (!success && attempts < MAX_RETRY_ATTEMPTS) {
            attempts++
            
            val command = Command(
                type = CommandType.SYNCHRONIZE,
                orders = batch
            )
            
            val result = sendFunction(command)
            println("[BluetoothMessageBuffer] Batch ${batchIndex+1}, attempt $attempts, success: $result")
            
            if (result == true) {
                success = true
            } else {
                // Jeśli wysyłanie nie powiodło się, czekamy przed ponowną próbą
                delay(1000)
            }
        }
        
        return success
    }
    
    /**
     * Pomocnicza funkcja do wykonania operacji w kontekście korutyny
     */
    private suspend fun <T> await(block: suspend () -> T): T {
        return block()
    }
}