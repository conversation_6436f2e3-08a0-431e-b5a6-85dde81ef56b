package pl.lazicki.bluetooth.data

import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import pl.lazicki.data.entity.OrderEntity
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * Bufor kołowy dla zmian pochodzących z interfejsu użytkownika.
 * Przechowuje zamówienia, które zostały zmienione przez użytkownika i oczekują na wysłanie.
 */
class UiChangeBuffer {
    // Bufor kołowy dla zmian UI - używamy ConcurrentLinkedQueue, która jest thread-safe
    private val buffer = ConcurrentLinkedQueue<OrderEntity>()
    
    // Mutex do synchronizacji dostępu do bufora
    private val bufferMutex = Mutex()
    
    // Maksymalny rozmiar bufora kołowego
    private val MAX_BUFFER_SIZE = 100
    
    // Przechowujemy czas dodania/modyfikacji zamówienia do bufora
    private val orderTimestamps = mutableMapOf<Int, Long>()
    
    /**
     * Dodaje zamówienie do bufora kołowego.
     * @param order Zamówienie do dodania
     */
    suspend fun addOrder(order: OrderEntity) {
        bufferMutex.withLock {
            println("[UiChangeBuffer] Adding order #${order.number}, state: ${order.state}")
            
            // Aktualizujemy timestamp dla tego zamówienia
            orderTimestamps[order.number] = System.currentTimeMillis()
            
            // Sprawdzamy, czy zamówienie już istnieje w buforze
            val existingOrder = buffer.find { it.number == order.number }
            if (existingOrder != null) {
                // Jeśli zamówienie już istnieje, usuwamy je
                buffer.remove(existingOrder)
            }
            
            // Dodajemy do bufora kołowego
            buffer.offer(order)
            
            // Jeśli bufor jest pełny, usuwamy najstarszy element
            while (buffer.size > MAX_BUFFER_SIZE) {
                buffer.poll()
            }
        }
    }
    
    /**
     * Pobiera zamówienia z bufora kołowego.
     * @param maxItems Maksymalna liczba zamówień do pobrania
     * @return Lista zamówień posortowana od najnowszych do najstarszych
     */
    suspend fun getOrders(maxItems: Int): List<OrderEntity> {
        return bufferMutex.withLock {
            val result = mutableListOf<OrderEntity>()
            
            // Pobieramy zamówienia z bufora
            while (result.size < maxItems && buffer.isNotEmpty()) {
                buffer.poll()?.let { result.add(it) }
            }
            
            // Sortujemy zamówienia według czasu dodania (od najnowszych do najstarszych)
            result.sortedByDescending { orderTimestamps[it.number] ?: 0L }
        }
    }
    
    /**
     * Dodaje zamówienia z powrotem do bufora.
     * Używane, gdy wysyłanie nie powiodło się.
     * @param orders Lista zamówień do dodania z powrotem
     */
    suspend fun returnOrders(orders: List<OrderEntity>) {
        bufferMutex.withLock {
            // Dodajemy zamówienia z powrotem do bufora
            orders.forEach { order ->
                buffer.offer(order)
            }
            
            // Jeśli bufor jest pełny, usuwamy najstarsze elementy
            while (buffer.size > MAX_BUFFER_SIZE) {
                buffer.poll()
            }
        }
    }
    
    /**
     * Sprawdza, czy bufor jest pusty.
     * @return true, jeśli bufor jest pusty, false w przeciwnym razie
     */
    suspend fun isEmpty(): Boolean {
        return bufferMutex.withLock { buffer.isEmpty() }
    }
    
    /**
     * Zwraca liczbę zamówień w buforze.
     * @return Liczba zamówień w buforze
     */
    suspend fun size(): Int {
        return bufferMutex.withLock { buffer.size }
    }
}