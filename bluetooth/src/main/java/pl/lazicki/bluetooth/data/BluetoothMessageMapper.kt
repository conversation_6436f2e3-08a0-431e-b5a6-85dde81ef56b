package pl.lazicki.bluetooth.data

import pl.lazicki.bluetooth.domain.BluetoothMessage
import pl.lazicki.bluetooth.domain.BluetoothMessageSerializer

fun String.toBluetoothMessage(isFromLocalUser: Boolean): BluetoothMessage? {
    return try {
        val message = BluetoothMessageSerializer.fromJson(this)
        message.copy(isFromLocalUser = isFromLocalUser)
    } catch (e: Exception) {
        null
    }
}

fun BluetoothMessage.toByteArray(): ByteArray {
    return BluetoothMessageSerializer.toJson(this).encodeToByteArray()
}
