plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.serialization)
    alias(libs.plugins.ksp)
    alias(libs.plugins.hilt)
}
apply(from = "$rootDir/android-library.gradle")

android.namespace = "pl.lazicki.bluetooth"

dependencies {
    implementation(libs.kotlinx.serialization.json)
    implementation(project(":data"))

    testImplementation(libs.junit)
}