package pl.lazicki.orderscreen.di

import android.content.Context
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import pl.lazicki.bluetooth.data.AndroidBluetoothController
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.orderscreen.core.coroutine.ContextProvider
import pl.lazicki.orderscreen.core.coroutine.ContextProviderImpl
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideContextProvider(): ContextProvider = ContextProviderImpl()

    @Provides
    @Singleton
    fun provideBluetoothController(
        @ApplicationContext context: Context
    ): BluetoothController = AndroidBluetoothController(context)

}
