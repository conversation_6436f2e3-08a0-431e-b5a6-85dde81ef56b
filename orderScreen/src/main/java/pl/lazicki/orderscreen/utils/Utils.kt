package pl.lazicki.orderscreen.utils

import android.app.Activity
import android.content.Context
import android.content.pm.ActivityInfo

fun rotateScreen(context: Context, orientation: Int) {
    val activity = context as? Activity ?: return
    when (orientation) {
        0 -> activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        90 -> activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        180 -> activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT
        270 -> activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE
    }
}