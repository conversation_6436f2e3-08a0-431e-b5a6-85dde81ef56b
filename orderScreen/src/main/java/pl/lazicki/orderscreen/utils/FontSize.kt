package pl.lazicki.orderscreen.utils

import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import kotlin.math.max
import kotlin.math.min

@Composable
fun responsiveFontSize(
    text: String,
    baseFontSize: TextUnit = 32.sp,
    minFontSize: TextUnit = 18.sp,
    maxFontSize: TextUnit = 52.sp,
    referenceWidthDp: Float = 400f
): Pair<String, TextUnit> {
    val configuration = LocalConfiguration.current
    var resultFontSize: TextUnit = baseFontSize

    BoxWithConstraints {
        val availableWidthDp = maxWidth.value
        resultFontSize = remember(text, configuration.screenWidthDp, availableWidthDp) {
            // Skalowanie względem szerokości ekranu
            val scaleFactor = configuration.screenWidthDp / referenceWidthDp
            val scaledByScreen = baseFontSize.value * scaleFactor

            // Skalowanie dodatkowe względem długości tekstu
            val scaleByText = if (text.isNotEmpty()) {
                val charFactor = availableWidthDp / (text.length * 10f) // ~10dp na znak
                scaledByScreen * charFactor.coerceAtMost(1f) // nie powiększamy ponad skalę ekranu
            } else scaledByScreen

            // Ograniczenie do min i max w sp
            val clampedFont = max(minFontSize.value, min(scaleByText, maxFontSize.value))
            clampedFont.sp
        }
    }
    return text to resultFontSize
}

