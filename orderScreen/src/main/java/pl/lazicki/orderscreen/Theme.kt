package pl.lazicki.orderscreen

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.tv.material3.MaterialTheme
import pl.lazicki.design.ColorSchemeTv
import pl.lazicki.design.typography.TvTypography

@Composable
fun OrderScreenTheme(
    isInDarkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit,
) {
    MaterialTheme(
        colorScheme = ColorSchemeTv,
        typography = TvTypography,
        content = content
    )
}