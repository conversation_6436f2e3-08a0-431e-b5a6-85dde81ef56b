package pl.lazicki.orderscreen.ui.orders

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.bluetooth.domain.ConnectionResult
import pl.lazicki.bluetooth.domain.Orders
import pl.lazicki.data.OrderRepository
import pl.lazicki.orderscreen.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class OrderViewModel @Inject constructor(
    private val bluetoothController: BluetoothController,
    private val orderRepository: OrderRepository,
) : MviViewModel<
        OrderContract.State,
        OrderContract.Action,
        OrderContract.Effect>(
    initialState = OrderContract.State(
        orders = Orders(
            inProgress = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "10"),
            ready = listOf("11", "12")
        )
    )
) {

//    private val _state = MutableStateFlow(OrderUiState(orders = Orders(emptyList(), emptyList())))
//    val state2 = combine(
//        bluetoothController.scannedDevices,
//        bluetoothController.pairedDevices,
//        _state
//    ) { scannedDevices, pairedDevices, state ->
//        state.copy(
//            scannedDevices = scannedDevices,
//            pairedDevices = pairedDevices,
//            messages = if (state.isConnected) state.messages else emptyList()
//        )
//    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), _state.value)

    private var deviceConnectionJob: Job? = null

    init {
        bluetoothController.isConnected.onEach { isConnected ->
            updateState { copy(isConnected = isConnected) }
        }.launchIn(viewModelScope)

        bluetoothController.errors.onEach { error ->
            updateState { copy(errorMessage = error) }
        }.launchIn(viewModelScope)

        startBluetoothServer()

        //TODO niech pobiera z data store ost podłączone urządzenie i jesli jest sparowane z tym urządzeniem
        //TODO to niech nie wywołuje metody niżej
        bluetoothController.makeDiscoverable()
    }

    private fun startBluetoothServer() {
        updateState { copy(isConnecting = true) }
        deviceConnectionJob = bluetoothController
            .startBluetoothServer()
            .listen()
    }

//    fun sendMessage(message: String) {
//        viewModelScope.launch {
//            val bluetoothMessage = bluetoothController.trySendMessage(message)
//            if (bluetoothMessage != null) {
//                _state.update {
//                    it.copy(
//                        messages = it.messages + bluetoothMessage
//                    )
//                }
//            }
//        }
//    }

//    fun connectToDevice(device: BluetoothDeviceDomain) {
//        _state.update { it.copy(isConnecting = true) }
//        deviceConnectionJob = bluetoothController
//            .connectToDevice(device)
//            .listen()
//    }

//    fun disconnectFromDevice() {
//        deviceConnectionJob?.cancel()
//        bluetoothController.closeConnection()
//        _state.update {
//            it.copy(
//                isConnecting = false,
//                isConnected = false
//            )
//        }
//    }

//    fun startScan() {
//        bluetoothController.startDiscovery()
//    }
//
//    fun stopScan() {
//        bluetoothController.stopDiscovery()
//    }

    private fun Flow<ConnectionResult>.listen(): Job {
        return onEach { result ->
            when (result) {
                ConnectionResult.ConnectionEstablished -> {
                    updateState {
                        copy(
                            isConnected = true,
                            isConnecting = false,
                            errorMessage = null
                        )
                    }
                }

                is ConnectionResult.TransferSucceeded -> {
                    updateState {
                        copy(
                            messages = state.value.messages + result.message
                        )
                    }
                }

                is ConnectionResult.Error -> {
                    updateState {
                        copy(
                            isConnected = false,
                            isConnecting = false,
                            errorMessage = result.message
                        )
                    }
                }
            }
        }.catch {
            bluetoothController.closeConnection()
            updateState {
                copy(
                    isConnected = false,
                    isConnecting = false,
                )
            }
        }.launchIn(viewModelScope)
    }
}
