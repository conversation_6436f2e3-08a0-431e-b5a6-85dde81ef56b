package pl.lazicki.orderscreen.ui.orders

import android.util.Log
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.bluetooth.domain.Command
import pl.lazicki.bluetooth.domain.CommandType
import pl.lazicki.bluetooth.domain.ConnectionResult
import pl.lazicki.data.repository.OrderRepository
import pl.lazicki.data.repository.ScreenSettingsRepository
import pl.lazicki.orderscreen.core.mvi.MviViewModel
import java.io.IOException
import javax.inject.Inject

@HiltViewModel
class OrderViewModel @Inject constructor(
    private val bluetoothController: BluetoothController,
    private val orderRepository: OrderRepository,
    private val screenSettingsRepository: ScreenSettingsRepository,
) : MviViewModel<
        OrderContract.State,
        OrderContract.Action,
        OrderContract.Effect>(
    initialState = OrderContract.State()
) {

    private var keepAliveJob: Job? = null

    init {
        observeScreenSettings()
        observeBluetoothSetup()
        observeOrders()
        startKeepAliveSignal()
    }

    private fun observeScreenSettings() {
        viewModelScope.launch(Dispatchers.IO) {
            screenSettingsRepository.observeScreenSettings().collect { screenSettings ->
                updateState {
                    copy(
                        fontSize = screenSettings?.fontSize ?: 32,
                        orderWidth = screenSettings?.orderWidth ?: 72,
                        titleInProgress = screenSettings?.titleInProgress ?: "Zamówione",
                        titleReady = screenSettings?.titleReady ?: "Odbierz",
                    )
                }
            }
        }
    }

    private fun observeOrders() {
        viewModelScope.launch(Dispatchers.IO) {
            orderRepository.observeAllOrders().collect { orders ->
                updateState { copy(orders = orders) }
            }
        }
    }

    private fun observeBluetoothSetup() {
        viewModelScope.launch(Dispatchers.IO) {
            bluetoothController.isConnected.onEach { isConnected ->
                updateState { copy(isConnected = isConnected) }
                println("[OrderViewModel][Screen] isConnected: $isConnected")
            }
        }
        viewModelScope.launch(Dispatchers.IO) {
            bluetoothController.errors.onEach { error ->
                updateState { copy(errorMessage = error) }
                println("[OrderViewModel][Screen] error: $error")
            }
        }

        startBluetoothServer()
        viewModelScope.launch(Dispatchers.IO) {
            val isLastDevicePairedAddress = screenSettingsRepository.getLastConnectedDevice()?.first
            if (bluetoothController.pairedDevices.value.any { it.address != isLastDevicePairedAddress }) {
                bluetoothController.makeDiscoverable()
            }
        }
    }

    private fun startBluetoothServer() {
        updateState { copy(isConnecting = true) }
        viewModelScope.launch(Dispatchers.IO) {
            try {
                var serverStartAttempts = 0
                val maxAttempts = 3  // Ograniczamy liczbę prób do 3

                while (serverStartAttempts < maxAttempts) {
                    try {
                        println("[OrderViewModel][Screen] Starting Bluetooth server, attempt ${serverStartAttempts + 1}")

                        bluetoothController
                            .startBluetoothServer()
                            .collect { result ->
                                when (result) {
                                    ConnectionResult.ConnectionEstablished -> {
                                        println("[OrderViewModel][Screen] ConnectionEstablished")
                                        updateState {
                                            copy(
                                                isConnected = true,
                                                isConnecting = false,
                                                errorMessage = null
                                            )
                                        }

                                        bluetoothController.currentConnectedDevice?.let { device ->
                                            screenSettingsRepository.saveLastConnectedDevice(
                                                deviceAddress = device.address,
                                                deviceName = device.name
                                            )
                                        }
                                    }

                                    is ConnectionResult.TransferSucceeded -> {
                                        val message = result.message

                                        // Przetwarzamy wiadomość tylko jeśli to nie jest potwierdzenie
                                        if (!message.command.isAcknowledgement) {

                                            when (message.command.type) {
                                                CommandType.ACK -> {
                                                    bluetoothController.trySendCommand(
                                                        Command(
                                                            type = CommandType.ACK,
                                                            isAcknowledgement = true,
                                                            acknowledgedMessageId = message.command.messageId
                                                        )
                                                    )
                                                }

                                                CommandType.SYNCHRONIZE -> {
                                                    message.command.orders?.let { orders ->
                                                        println("[OrderViewModel][Screen] Received ${orders.size} orders")
                                                        orderRepository.synchronizeOrders(orders)
                                                    }
                                                }

                                                CommandType.SETTINGS -> {
                                                    message.command.settings.let { settings ->
                                                        println("[OrderViewModel][Screen] Received settings: $settings")
                                                        with(screenSettingsRepository) {
                                                            updateOrderWidth(
                                                                settings?.orderWidth ?: 72
                                                            )
                                                            updateFontSize(settings?.fontSize ?: 32)
                                                            updateTitleInProgress(
                                                                settings?.inProgressTitle
                                                                    ?: "Zamówione"
                                                            )
                                                            updateTitleReady(
                                                                settings?.readyTitle ?: "Odbierz"
                                                            )
                                                        }
                                                    }
                                                }

                                                CommandType.DELETE_ALL_ORDERS -> {
                                                    orderRepository.deleteAllOrders()
                                                }

                                                CommandType.ROTATE_SCREEN -> {
                                                    message.command.settings.let { settings ->
                                                        with(screenSettingsRepository) {
                                                            updateScreenOrientation(
                                                                settings?.screenOrientation ?: 0
                                                            )
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    is ConnectionResult.Error -> {
                                        println("[OrderViewModel][Screen] ConnectionResult error: ${result.message}")
                                        updateState {
                                            copy(
                                                isConnected = false,
                                                isConnecting = false,
                                                errorMessage = result.message
                                            )
                                        }

                                        // Przerywamy kolekcję, aby przejść do następnej próby
                                        throw IOException("Server error: ${result.message}")
                                    }
                                }
                            }

                        // Jeśli dotarliśmy tutaj, to kolekcja zakończyła się normalnie
                        // Zwiększamy licznik prób i próbujemy ponownie po opóźnieniu
                        serverStartAttempts++
                        delay(5000)

                    } catch (e: Exception) {
                        println("[OrderViewModel][Screen] Error in Bluetooth server: ${e.message}")

                        // Zwiększamy licznik prób
                        serverStartAttempts++

                        if (serverStartAttempts < maxAttempts) {
                            // Czekamy przed kolejną próbą
                            delay(5000)
                        }
                    }
                }

                // Jeśli dotarliśmy tutaj, to znaczy, że wyczerpaliśmy wszystkie próby
                println("[OrderViewModel][Screen] Failed to start server after $maxAttempts attempts")
                updateState {
                    copy(
                        isConnected = false,
                        isConnecting = false,
                        errorMessage = "Failed to start server after $maxAttempts attempts"
                    )
                }

                // Czekamy dłużej przed ponownym uruchomieniem całej procedury
                delay(30000)
                startBluetoothServer()

            } catch (e: Exception) {
                println("[OrderViewModel][Screen] Unexpected error: ${e.message}")
                updateState {
                    copy(
                        isConnected = false,
                        isConnecting = false,
                        errorMessage = "Unexpected error: ${e.message}"
                    )
                }

                // Czekamy przed ponownym uruchomieniem
                delay(30000)
                startBluetoothServer()
            }
        }
    }

    private fun startKeepAliveSignal() {
        if (keepAliveJob?.isActive == true) return

        keepAliveJob = viewModelScope.launch {
            Log.d("OrderViewModel", "KeepAlive signal job started.")
            try {
                while (true) {
                    delay(60000L)
                    updateState { copy(orders = state.value.orders) }
                    Log.d("OrderViewModel", "KeepAlive signal tick")
                }
            } catch (e: kotlinx.coroutines.CancellationException) {
                Log.d("OrderViewModel", "KeepAlive signal job cancelled.")
            }
        }
    }

    private fun stopKeepAliveSignal() {
        keepAliveJob?.cancel()
        keepAliveJob = null
        Log.d("OrderViewModel", "KeepAlive signal job stopped.")
    }

    override fun onCleared() {
        stopKeepAliveSignal()
        super.onCleared()
    }
}
