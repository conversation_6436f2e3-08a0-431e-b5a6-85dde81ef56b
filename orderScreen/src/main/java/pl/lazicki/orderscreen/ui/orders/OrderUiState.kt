package pl.lazicki.orderscreen.ui.orders

import pl.lazicki.bluetooth.domain.BluetoothDeviceDomain
import pl.lazicki.bluetooth.domain.BluetoothMessage
import pl.lazicki.bluetooth.domain.Orders

data class OrderUiState(
    val scannedDevices: List<BluetoothDeviceDomain> = emptyList(),
    val pairedDevices: List<BluetoothDeviceDomain> = emptyList(),
    val isConnected: Boolean = false,
    val isConnecting: Boolean = false,
    val errorMessage: String? = null,
    val messages: List<BluetoothMessage> = emptyList(),
    val orders: Orders,
    val appVersion: String = "1.0.0"
)
