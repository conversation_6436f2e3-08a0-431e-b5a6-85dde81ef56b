package pl.lazicki.orderscreen.ui.orders

import pl.lazicki.bluetooth.domain.BluetoothDeviceDomain
import pl.lazicki.data.entity.OrderEntity

object OrderContract {
    data class State(
        val scannedDevices: List<BluetoothDeviceDomain> = emptyList(),
        val pairedDevices: List<BluetoothDeviceDomain> = emptyList(),
        val isConnected: Boolean = false,
        val isConnecting: Boolean = false,
        val errorMessage: String? = null,
        val orders: List<OrderEntity> = emptyList(),
        val appVersion: String = "1.0.0",
        val fontSize: Int = 32,
        val orderWidth: Int = 72,
        val titleInProgress: String = "Zamówione",
        val titleReady: String = "Odbierz",
    )

    sealed interface Action
    sealed interface Effect
}
