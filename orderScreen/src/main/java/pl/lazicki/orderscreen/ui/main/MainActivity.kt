package pl.lazicki.orderscreen.ui.main

import android.Manifest
import android.app.admin.DevicePolicyManager
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import android.view.KeyEvent
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.launch
import androidx.compose.runtime.mutableFloatStateOf
import androidx.core.view.WindowCompat
import dagger.hilt.android.AndroidEntryPoint
import pl.lazicki.orderscreen.OrderScreenTheme
import pl.lazicki.orderscreen.R
import pl.lazicki.orderscreen.core.KioskService
import pl.lazicki.orderscreen.core.splash.SplashActivity

@AndroidEntryPoint
class MainActivity : SplashActivity() {

    private val bluetoothManager by lazy {
        applicationContext.getSystemService(BluetoothManager::class.java)
    }
    private val bluetoothAdapter by lazy {
        bluetoothManager?.adapter
    }

    private val isBluetoothEnabled: Boolean
        get() = bluetoothAdapter?.isEnabled == true

    private val TAG = "OverlayPermission"

    private lateinit var requestOverlayPermissionLauncher: ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE

        val enableBluetoothLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { /* Not needed */ }

        val permissionLauncher = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { perms ->
            val canEnableBluetooth = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                perms[Manifest.permission.BLUETOOTH_CONNECT] == true
            } else {
                perms[Manifest.permission.BLUETOOTH] == true &&
                        perms[Manifest.permission.ACCESS_COARSE_LOCATION] == true
            }

            if (canEnableBluetooth && !isBluetoothEnabled) {
                enableBluetoothLauncher.launch(
                    Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                )
            }
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissionLauncher.launch(
                arrayOf(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_ADVERTISE,
                )
            )
        } else {
            permissionLauncher.launch(
                arrayOf(
                    Manifest.permission.BLUETOOTH,
                    Manifest.permission.BLUETOOTH_ADMIN,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                )
            )
        }

        var deviceName = "unknown"
        try {
            deviceName = bluetoothAdapter?.name ?: "unknown"
            if (!deviceName.contains("KolejkaSystem.pl")) {
                deviceName =
                    "KolejkaSystem.pl - #${System.currentTimeMillis().toString().takeLast(4)}"
                bluetoothAdapter?.setName(deviceName)
            }
        } catch (e: SecurityException) {
            println("Błąd uprawnień podczas zmiany nazwy Bluetooth: ${e.message}")
        } catch (e: Exception) {
            println("Wystąpił nieoczekiwany błąd podczas zmiany nazwy Bluetooth: ${e.message}")
        }

        checkAndRequestOverlayPermission()
        KioskService.start(this)

        setContent {
            OrderScreenTheme {
                MainScreen(
                    deviceName = deviceName,
                )
            }
        }
    }

    private var exitCounter: Int = 0
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        println("KEY pressed, keyCode: $keyCode")
        if (keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
            println("KEYCODE_DPAD_DOWN pressed, exitCounter: $exitCounter")
            exitCounter++
            if (exitCounter >= 10) {
                KioskService.stop(this)
                finish()
            }
            return true
        } else {
            exitCounter = 0
            return true
        }
//        return when (keyCode) {
//
//
//            KeyEvent.KEYCODE_DPAD_DOWN -> {
//
//            }
//            KeyEvent.KEYCODE_BACK,
//            KeyEvent.KEYCODE_VOLUME_DOWN,
//            KeyEvent.KEYCODE_VOLUME_UP,
//            KeyEvent.KEYCODE_HOME,
//            KeyEvent.KEYCODE_MENU,
//            KeyEvent.KEYCODE_POWER,
//            KeyEvent.KEYCODE_APP_SWITCH -> {
//                exitCounter = 0
//                true // Zablokuj przyciski
//            }
//            else -> {
//                exitCounter = 0
//                super.onKeyDown(keyCode, event)
//            }
//        }
    }

    private fun checkAndRequestOverlayPermission() {
        requestOverlayPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) {
            if (Settings.canDrawOverlays(this)) {
                Log.i(TAG, "Uprawnienie SYSTEM_ALERT_WINDOW zostało przyznane.")
                Toast.makeText(this, "Uprawnienie do wyświetlania nad innymi aplikacjami przyznane.", Toast.LENGTH_SHORT).show()
            } else {
                Log.w(TAG, "Uprawnienie SYSTEM_ALERT_WINDOW NIE zostało przyznane.")
                Toast.makeText(this, "Uprawnienie do wyświetlania nad innymi aplikacjami nie zostało przyznane.", Toast.LENGTH_LONG).show()
            }
        }

        if (!Settings.canDrawOverlays(this)) {
            Log.d(TAG, "Aplikacja nie ma uprawnienia SYSTEM_ALERT_WINDOW. Przekierowuję do ustawień.")
            val intent = Intent(
                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                Uri.parse("package:$packageName")
            )
            try {
                requestOverlayPermissionLauncher.launch(intent)
            } catch (e: Exception) {
                Log.e(TAG, "Nie można uruchomić ekranu ustawień dla SYSTEM_ALERT_WINDOW: ${e.message}", e)
                Toast.makeText(this, "Nie można otworzyć ustawień uprawnień.", Toast.LENGTH_LONG).show()
            }
        } else {
            Log.d(TAG, "Aplikacja już ma uprawnienie SYSTEM_ALERT_WINDOW.")
        }
    }
}
