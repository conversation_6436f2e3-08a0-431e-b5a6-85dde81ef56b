package pl.lazicki.orderscreen.ui.main

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.data.repository.ScreenSettingsRepository
import pl.lazicki.orderscreen.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val screenSettingsRepository: ScreenSettingsRepository,
    private val bluetoothController: BluetoothController,
) : MviViewModel<MainContract.State, MainContract.Action, MainContract.Effect>(
    initialState = MainContract.State()
) {

    init {
        observeScreenSettings()
        viewModelScope.launch(Dispatchers.IO) {
            println("[MainViewModel] getLastConnectedDevice: ${screenSettingsRepository.getLastConnectedDevice()?.second}")
            if (screenSettingsRepository.getLastConnectedDevice() != null) {
                pushEffect(MainContract.Effect.NavigateToOrderScreen)
            } else {
                pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
            }
        }
    }

    override fun onAction(action: MainContract.Action) {
        when(action) {
            is MainContract.Action.SetDeviceName -> screenSettingsRepository.setDeviceName(action.deviceName)
        }
    }

    @OptIn(FlowPreview::class)
    private fun observeScreenSettings() {
        viewModelScope.launch {
            screenSettingsRepository
                .observeScreenSettings()
                .distinctUntilChanged()
                .debounce(500)
                .collect { screenSettings ->
                    println("[MainViewModel] observeScreenSettings screenOrientation: ${screenSettings?.screenOrientation}")
                    updateState { copy(screenOrientation = screenSettings?.screenOrientation ?: 0) }
                    pushEffect(
                        MainContract.Effect.RotateScreen(
                            screenSettings?.screenOrientation ?: 0
                        )
                    )
                }
        }
    }

    override fun onCleared() {
        super.onCleared()
        println("[MainViewModel] onCleared")
        bluetoothController.release()
    }
}
