package pl.lazicki.orderscreen.ui.main

import dagger.hilt.android.lifecycle.HiltViewModel
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.orderscreen.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val screenSettingsRepository: ScreenSettingsRepository,
    private val bluetoothController: BluetoothController,
) : MviViewModel<MainContract.State, MainContract.Action, MainContract.Effect>(
    initialState = MainContract.State(
        screenOrientation = screenSettingsRepository.screenOrientation
    )
) {
    //TODO dodaj obserwer na screen orientation.
    //TODO niech obserwacja jest z rooma i po odbiorze polecenia z bluetooth to niech sie nadpisuje

    init {
        if (bluetoothController.pairedDevices.value.isNotEmpty()) {
            pushEffect(MainContract.Effect.NavigateToOrderScreen)
        } else {
            pushEffect(MainContract.Effect.NavigateToWelcomeScreen)
        }
    }

    override fun onAction(action: MainContract.Action) {
        super.onAction(action)
    }


    override fun onCleared() {
        super.onCleared()
        bluetoothController.release()
    }
}
