package pl.lazicki.orderscreen.ui.orders

import android.app.Activity
import android.util.Log
import android.view.WindowManager
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.annotation.RootNavGraph
import pl.lazicki.data.entity.OrderEntity
import pl.lazicki.data.entity.OrderState

@RootNavGraph(start = true)
@Destination
@Composable
fun OrdersScreen(
    viewModel: OrderViewModel = hiltViewModel(),
) {
    val uiState: OrderContract.State by viewModel.state.collectAsStateWithLifecycle()

    OrdersScreenContent(uiState)
}

@Composable
fun OrdersScreenContent(
    state: OrderContract.State,
) {

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 8.dp, top = 8.dp, end = 8.dp)
                .height(IntrinsicSize.Min),
        ) {
            Box(
                modifier = Modifier
                    .weight(0.7f)
                    .fillMaxHeight()
                    .background(Color(0xFF625C5C))
                    .padding(8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = state.titleInProgress.uppercase(),
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    maxLines = 2,
                    color = Color.White,
                    fontSize = state.fontSize.sp
                )
            }
            Box(
                modifier = Modifier
                    .weight(0.3f)
                    .fillMaxHeight()
                    .background(Color(0xFF79B407))
                    .padding(8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = state.titleReady.uppercase(),
                    textAlign = TextAlign.Center,
                    maxLines = 2,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    fontSize = state.fontSize.sp
                )
            }
        }
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(start = 8.dp, bottom = 8.dp, end = 8.dp),
        ) {
            LazyVerticalGrid(
                columns = GridCells.Adaptive(minSize = state.orderWidth.dp),
                modifier = Modifier
                    .weight(0.7f),
                contentPadding = PaddingValues(vertical = 10.dp, horizontal = 8.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                items(
                    state.orders
                        .filter { it.state == OrderState.InProgress }
                        .map { it.number }
                ) { number ->
                    OrderItem(
                        fontSize = state.fontSize,
                        isReady = false,
                        number = number.toString(),
                        modifier = Modifier.widthIn(min = 72.dp, max = 150.dp)
                    )
                }
            }
            LazyVerticalGrid(
                columns = GridCells.Adaptive(minSize = state.orderWidth.dp),
                modifier = Modifier
                    .weight(0.3f),
                contentPadding = PaddingValues(10.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                items(
                    state.orders
                        .filter { it.state == OrderState.Ready }
                        .map { it.number }
                ) { number ->
                    OrderItem(
                        fontSize = state.fontSize,
                        isReady = true,
                        number = number.toString(),
                        modifier = Modifier.widthIn(min = 72.dp, max = 150.dp)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))
        Row(
            modifier = Modifier
                .height(18.dp)
                .fillMaxWidth()
                .padding(start = 2.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "KolejkaSystem.pl v${state.appVersion}",
                fontSize = 8.sp,
                color = Color.LightGray,
                modifier = Modifier
            )

            if (state.isConnecting) {
                Spacer(modifier = Modifier.weight(1f))
                Row(
                    modifier = Modifier,
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    CircularProgressIndicator(
                        strokeWidth = 2.dp,
                        modifier = Modifier
                            .size(20.dp)
                            .padding(6.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun OrderItem(
    fontSize: Int,
    isReady: Boolean,
    number: String,
    modifier: Modifier = Modifier
) {
    val (backgroundColor, borderColor) = if (isReady) {
        Color(0xFF79B407) to Color(0xFF79B407)
    } else {
        Color.Transparent to Color(0xFF625C5C)
    }
    val textColor = if (isReady) {
        Color.White
    } else {
        Color.Black
    }

    Box(
        modifier = modifier
            .background(backgroundColor, RoundedCornerShape(8.dp))
            .border(1.dp, borderColor, RoundedCornerShape(8.dp))
            .padding(vertical = 2.dp, horizontal = 10.dp)
    ) {
        Text(
            text = number,
            color = textColor,
            textAlign = TextAlign.Center,
            maxLines = 1,
            fontSize = fontSize.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .padding(8.dp)
                .fillMaxWidth()
        )
    }
}

@Preview(
    widthDp = 540,
    heightDp = 960,
    device = Devices.TV_1080p
)
@Composable
fun OrdersScreenPreview() {
    OrdersScreenContent(
        state = OrderContract.State(
            isConnecting = true,
            orders = listOf(
                OrderEntity(0, 1, OrderState.Deleted),
                OrderEntity(1, 2, OrderState.Ready),
                OrderEntity(2, 3, OrderState.Ready),
                OrderEntity(3, 4, OrderState.InProgress),
                OrderEntity(4, 15, OrderState.InProgress),
                OrderEntity(5, 16, OrderState.InProgress),
                OrderEntity(6, 171, OrderState.InProgress),
            )
        ),
    )
}