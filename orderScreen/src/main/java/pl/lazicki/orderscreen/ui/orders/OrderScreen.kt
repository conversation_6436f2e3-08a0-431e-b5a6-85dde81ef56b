package pl.lazicki.orderscreen.ui.orders

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ramcosta.composedestinations.annotation.Destination
import pl.lazicki.bluetooth.domain.Orders
import pl.lazicki.orderscreen.ui.welcome.WelcomeContract

@Destination
@Composable
fun OrdersScreen(
    viewModel: OrderViewModel = hiltViewModel(),
) {
    val uiState: OrderContract.State by viewModel.state.collectAsStateWithLifecycle()

    OrdersScreenContent(uiState)
}

@Composable
fun OrdersScreenContent(
    state: OrderContract.State,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, top = 16.dp, end = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Zamówione".uppercase(),
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center,
                maxLines = 1,
                color = Color.White,
                fontSize = 32.sp,
                modifier = Modifier
                    .weight(0.7f)
                    .background(Color(0xFF625C5C))
                    .padding(8.dp)
            )
            Text(
                text = "Odbierz".uppercase(),
                textAlign = TextAlign.Center,
                maxLines = 1,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                fontSize = 32.sp,
                modifier = Modifier
                    .weight(0.3f)
                    .background(Color(0xFF79B407))
                    .padding(8.dp)
            )

        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, bottom = 16.dp, end = 16.dp),
        ) {
            LazyVerticalGrid(
                columns = GridCells.Adaptive(minSize = 92.dp),
                modifier = Modifier
                    .weight(0.7f),
                contentPadding = PaddingValues(vertical = 16.dp, horizontal = 8.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                items(state.orders.inProgress) { order ->
                    OrderItem(isReady = false, number = order)
                }
            }
            LazyVerticalGrid(
                columns = GridCells.Adaptive(minSize = 92.dp),
                modifier = Modifier
                    .weight(0.3f),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
            ) {
                items(state.orders.ready) { order ->
                    OrderItem(isReady = true, number = order)
                }
            }
        }

        Spacer(modifier = Modifier.weight(1f))
        Row(
            modifier = Modifier
                .height(18.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            androidx.tv.material3.Text(
                text = "KolejkaSystem.pl",
                fontSize = 12.sp,
                color = Color.LightGray,
                modifier = Modifier
            )
            androidx.tv.material3.Text(
                text = "v${state.appVersion}",
                fontSize = 12.sp,
                color = Color.LightGray,
                modifier = Modifier.padding(start = 14.dp)
            )

            if (state.isConnecting) {
                Spacer(modifier = Modifier.weight(1f))
                Row(
                    modifier = Modifier,
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    CircularProgressIndicator(
                        strokeWidth = 2.dp,
                        modifier = Modifier.size(24.dp).padding(6.dp)
                    )
                    androidx.tv.material3.Text(
                        text = "Łączenie...",
                        fontSize = 12.sp,
                        color = Color.Black
                    )
                }
            }
        }
    }
}

@Composable
fun OrderItem(
    isReady: Boolean,
    number: String,
) {
    val (backgroundColor, borderColor) = if (isReady) {
        Color(0xFF79B407) to Color(0xFF79B407)
    } else {
        Color.Transparent to Color(0xFF625C5C)
    }
    val textColor = if (isReady) {
        Color.White
    } else {
        Color.Black
    }

    Box(
        modifier = Modifier
            .widthIn(min = 92.dp, max = 100.dp)
            .background(backgroundColor, RoundedCornerShape(8.dp))
            .border(1.dp, borderColor, RoundedCornerShape(8.dp))
            .padding(vertical = 2.dp, horizontal = 10.dp)
    ) {
        Text(
            text = number,
            color = textColor,
            textAlign = TextAlign.Center,
            maxLines = 1,
            fontSize = 32.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .padding(8.dp)
                .fillMaxWidth()
        )
    }
}

@Preview(
    widthDp = 540,
    heightDp = 960,
    device = Devices.TV_1080p
)
@Composable
fun OrdersScreenPreview() {
    OrdersScreenContent(
        state = OrderContract.State(
            orders = Orders(
                inProgress = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "10"),
                ready = listOf("11", "12", "13", "14", "15", "16", "17", "18", "19", "20")
            )
        )
    )
}