package pl.lazicki.orderscreen.ui.main

import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.ramcosta.composedestinations.DestinationsNavHost
import pl.lazicki.orderscreen.OrderScreenTheme
import pl.lazicki.orderscreen.core.mvi.EffectCollector
import pl.lazicki.orderscreen.ui.NavGraphs
import pl.lazicki.orderscreen.ui.appCurrentDestinationAsState
import pl.lazicki.orderscreen.ui.destinations.OrdersScreenDestination
import pl.lazicki.orderscreen.ui.destinations.WelcomeScreenDestination
import pl.lazicki.orderscreen.ui.startAppDestination

@Composable
fun MainScreen(
    deviceName: String,
    viewModel: MainViewModel = hiltViewModel()
) {
    viewModel.onAction(MainContract.Action.SetDeviceName(deviceName))
    val navController = rememberNavController()
    val context = LocalContext.current

    // we can fetch state updates and set top level ui adjustments for screen destinations.
    // also look [DestinationExt.kt]
    val destination = navController.appCurrentDestinationAsState().value
        ?: NavGraphs.root.startAppDestination

    val uiState: MainContract.State by viewModel.state.collectAsStateWithLifecycle()

    EffectCollector(effect = viewModel.effect) { effect ->
        when (effect) {
            is MainContract.Effect.RotateScreen -> {
//                rotationState.floatValue = effect.orientation.toFloat()
//                onRotateRequested(effect.orientation.toFloat())
//                rotateScreen(context, effect.orientation)
            }
            MainContract.Effect.NavigateToOrderScreen -> {
                if (navController.currentDestination?.route != OrdersScreenDestination.route) {
                    navController.navigate(OrdersScreenDestination.route)
                }
            }

            MainContract.Effect.NavigateToWelcomeScreen -> {
                if (navController.currentDestination?.route != WelcomeScreenDestination.route) {
                    navController.navigate(WelcomeScreenDestination.route)
                }
            }
        }
    }

    MainScreenContent(
        navController = navController,
        uiState = uiState,
        onAction = viewModel::onAction
    )
}

@Composable
fun MainScreenContent(
    navController: NavHostController,
    uiState: MainContract.State,
    onAction: (MainContract.Action) -> Unit,
) {
    Surface(
        modifier = Modifier,
        color = MaterialTheme.colorScheme.background
    ) {
        DestinationsNavHost(
            navGraph = NavGraphs.root,
            navController = navController
        )
    }
}

@Preview
@Composable
fun MainPreview() {
    OrderScreenTheme {
        MainScreenContent(
            navController = rememberNavController(),
            uiState = MainContract.State(screenOrientation = 0),
            onAction = {}
        )
    }
}
