package pl.lazicki.orderscreen.ui.welcome

object WelcomeContract {
    data class State(
        val isLoading: Boolean = true,
        val currentInstruction: Int = 1,
        val currentOrientation: Int = 0 // 0, 90, 180, 270
    )

    sealed interface Action {
        data class OnInstructionClick(val instructionNumber: Int) : Action
        data object OnRotateScreen : Action
    }

    sealed interface Effect {
        data object NavigateToOrderScreen : Effect
        data class RotateScreen(val orientation: Int) : Effect
    }
}
