package pl.lazicki.orderscreen.ui.main

object MainContract {

    data class State(
        val screenOrientation: Int = 0,
    )

    sealed interface Action {
        data class SetDeviceName(val deviceName: String) : Action
    }

    sealed interface Effect {
        data class RotateScreen(val orientation: Int) : Effect
        data object NavigateToOrderScreen : Effect
        data object NavigateToWelcomeScreen : Effect
    }
}
