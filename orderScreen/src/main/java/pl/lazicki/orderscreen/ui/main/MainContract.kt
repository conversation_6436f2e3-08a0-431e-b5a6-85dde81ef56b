package pl.lazicki.orderscreen.ui.main

import pl.lazicki.orderscreen.ui.welcome.WelcomeContract.Effect

object MainContract {

    data class State(
        val screenOrientation: Int,
    )

    sealed interface Action {
        data class SetDescription(val description: String) : Action
        data object OnAddEventClick : Action
    }

    sealed interface Effect {
        data class RotateScreen(val orientation: Int) : Effect
        data object NavigateToOrderScreen: Effect
    }
}
