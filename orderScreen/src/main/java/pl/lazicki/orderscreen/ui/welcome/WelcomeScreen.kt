package pl.lazicki.orderscreen.ui.welcome

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.CircularProgressIndicator
import androidx.tv.material3.Text
import androidx.tv.material3.Button
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.tv.material3.Icon

@Composable
fun WelcomeScreen(
    state: WelcomeUiState,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF0f172a)),
        verticalArrangement = Arrangement.Center
    ) {
        InstructionTitle()
//        InstructionBodyOne()
//        InstructionBodySecond()
//        InstructionBodyThird()
//        InstructionBodyFourth()
    }
}

@Composable
fun InstructionBodyOne() {
    Text(
        text = "Użyj pilota dołączonego do zestawu i kliknij OK",
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = 42.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 108.dp, horizontal = 44.dp)
    )
}

@Composable
fun InstructionBodySecond() {
    Text(
        text = "Wybierz orientacje ekranu",
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = 42.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 108.dp, horizontal = 44.dp)
    )

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        Button(
            onClick = {},
            modifier = Modifier
                .width(200.dp)
                .height(52.dp)
        ) {
            Text(
                text = "Obróć o 90 st",
                fontSize = 28.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxSize()
            )
        }
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 34.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        Button(
            onClick = {},
            modifier = Modifier
                .width(200.dp)
                .height(52.dp)
        ) {
            Text(
                text = "Zatwierdź",
                fontSize = 28.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxSize()
            )
        }
    }
}

@Composable
fun InstructionBodyThird() {
    Text(
        text = "Pobierz aplikację mobilną na tablet lub telefon",
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = 42.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 108.dp, start = 44.dp, end = 44.dp)
    )
    Text(
        text = "Aplikacja mobilna służy do zarządzania zamówieniami i sterowaniem ekranem",
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = 34.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 58.dp, top = 28.dp, start = 44.dp, end = 44.dp)
    )
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Filled.Info,
            contentDescription = "QR code",
            modifier = Modifier
                .width(100.dp)
                .height(100.dp)
        )
    }
}

@Composable
fun InstructionBodyFourth() {
    Text(
        text = "Połącz się z aplikacją mobilną",
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = 42.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 108.dp, start = 44.dp, end = 44.dp)
    )
    Text(
        text = "Uruchom aplikację mobilną i naciśnij przycisk 'Połącz z ekranem'",
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = 34.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 58.dp, top = 28.dp, start = 44.dp, end = 44.dp)
    )

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        if (false) {//state.isConnecting) {
            CircularProgressIndicator(
                strokeWidth = 2.dp,
                modifier = Modifier.size(48.dp).padding(6.dp)
            )
        } else {
            Button(
                onClick = {},
                modifier = Modifier
                    .width(200.dp)
                    .height(52.dp)
            ) {
                Text(
                    text = "Zatwierdź",
                    fontSize = 28.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxSize()
                )
            }
        }
    }
}

@Composable
fun InstructionTitle() {
    Text(
        text = buildAnnotatedString {
            withStyle(style = SpanStyle(color = Color.White)) {
                append("Kolejka")
            }
            withStyle(style = SpanStyle(color = Color(0xFFf97316))) {
                append("System")
            }
        },
        fontWeight = FontWeight.Bold,
        textAlign = TextAlign.Center,
        fontSize = 54.sp,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 108.dp)
    )
}
@Preview(
    widthDp = 540,
    heightDp = 960,
    device = Devices.TV_1080p
)
@Composable
fun WelcomeScreenPreview() {
    WelcomeScreen(
        state = WelcomeUiState(),
    )
}