package pl.lazicki.orderscreen.ui.welcome

import androidx.compose.foundation.background
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.input.key.type
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Devices
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.tv.material3.Button
import androidx.tv.material3.ButtonDefaults
import androidx.tv.material3.Icon
import androidx.tv.material3.Text
import coil.compose.AsyncImage
import com.ramcosta.composedestinations.annotation.Destination
import com.ramcosta.composedestinations.annotation.RootNavGraph
import com.ramcosta.composedestinations.navigation.DestinationsNavigator
import pl.lazicki.orderscreen.utils.responsiveFontSize
import pl.lazicki.orderscreen.OrderScreenTheme
import pl.lazicki.orderscreen.R
import pl.lazicki.orderscreen.components.KSButton
import pl.lazicki.orderscreen.core.mvi.EffectCollector
import pl.lazicki.orderscreen.ui.destinations.OrdersScreenDestination
import pl.lazicki.orderscreen.utils.rotateScreen

@Destination
@Composable
fun WelcomeScreen(
    viewModel: WelcomeViewModel = hiltViewModel(),
    navigator: DestinationsNavigator,
) {
    val uiState: WelcomeContract.State by viewModel.state.collectAsStateWithLifecycle()
    val context = LocalContext.current

    EffectCollector(effect = viewModel.effect) { effect ->
        when (effect) {
            is WelcomeContract.Effect.NavigateToOrderScreen -> {
                navigator.navigate(OrdersScreenDestination)
            }

            is WelcomeContract.Effect.RotateScreen -> {
                rotateScreen(context, effect.orientation)
            }
        }
    }

    WelcomeScreenContent(
        uiState = uiState,
        onAction = viewModel::onAction
    )
}

@Composable
fun WelcomeScreenContent(
    uiState: WelcomeContract.State,
    onAction: (WelcomeContract.Action) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF0f172a)),
        verticalArrangement = Arrangement.Center
    ) {
        InstructionTitle()
        when (uiState.currentInstruction) {
            1 -> InstructionBodyOne(onAction)
//            2 -> InstructionBodySecond(onAction)
            3 -> InstructionBodyThird(onAction)
            4 -> InstructionBodyFourth(uiState, onAction)
        }
    }
}

@Composable
fun InstructionBodyOne(
    onAction: (WelcomeContract.Action) -> Unit
) {
    val (text, fontSize) = responsiveFontSize("Użyj pilota dołączonego do zestawu i kliknij OK")
    Text(
        text = text,
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = fontSize,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 108.dp, horizontal = 44.dp)
    )
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 34.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        KSButton(
            text = "OK",
            onClick = { onAction(WelcomeContract.Action.OnInstructionClick(3)) },
            modifier = Modifier
                .width(200.dp)
                .height(52.dp)
        )
    }
}

@Composable
fun InstructionBodySecond(
    onAction: (WelcomeContract.Action) -> Unit
) {
    val (text, fontSize) = responsiveFontSize("Wybierz orientacje ekranu")
    Text(
        text = text,
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = fontSize,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 108.dp, horizontal = 44.dp)
    )

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        KSButton(
            text = "Obróć o 90 st",
            onClick = { onAction(WelcomeContract.Action.OnRotateScreen) },
            modifier = Modifier
                .width(200.dp)
                .height(52.dp)
        )
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 34.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        KSButton(
            text = "Zatwierdź",
            onClick = { onAction(WelcomeContract.Action.OnInstructionClick(3)) },
            modifier = Modifier
                .width(200.dp)
                .height(52.dp)
        )
    }
}

@Composable
fun InstructionBodyThird(
    onAction: (WelcomeContract.Action) -> Unit
) {
    val (textDownload, fontSizeDownload) = responsiveFontSize("Pobierz aplikację mobilną na tablet lub telefon")
    Text(
        text = textDownload,
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = fontSizeDownload,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 42.dp, start = 44.dp, end = 44.dp)
    )

    val (text, fontSize) = responsiveFontSize(
        text = "Aplikacja mobilna służy do zarządzania zamówieniami i sterowania ekranem",
        baseFontSize = 22.sp
    )
    Text(
        text = text,
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = fontSize,
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 46.dp, top = 12.dp, start = 44.dp, end = 44.dp)
    )
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            AsyncImage(
                model = R.drawable.qr_code,
                contentDescription = "QR code",
                modifier = Modifier
                    .size(140.dp)
                    .background(Color.White)
            )
            val (qrText, qrFontSize) = responsiveFontSize(
                text = "www.kolejkasystem.pl/aplikacja",
                baseFontSize = 18.sp
            )
            Text(
                text = qrText,
                fontWeight = FontWeight.Normal,
                textAlign = TextAlign.Center,
                color = Color.White,
                fontSize = qrFontSize,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 4.dp)
            )
        }
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 46.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        KSButton(
            text = "Aplikacja pobrana",
            onClick = { onAction(WelcomeContract.Action.OnInstructionClick(4)) },
            modifier = Modifier
                .width(400.dp)
                .height(56.dp)
        )
    }
}

@Composable
fun InstructionBodyFourth(
    uiState: WelcomeContract.State,
    onAction: (WelcomeContract.Action) -> Unit
) {
    val (headerText, headerFontSize) = responsiveFontSize(
        text = "Połącz się z aplikacją mobilną",
        baseFontSize = 36.sp
    )
    Text(
        text = headerText,
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = headerFontSize,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 88.dp, start = 44.dp, end = 44.dp)
    )

    val (descriptionText, descriptionFontSize) = responsiveFontSize(
        text = "Uruchom aplikację mobilną i z listy urządzeń wybierz\n\n${uiState.deviceName}",
        baseFontSize = 24.sp
    )
    Text(
        text = descriptionText,
        fontWeight = FontWeight.Normal,
        textAlign = TextAlign.Center,
        color = Color.White,
        fontSize = descriptionFontSize,
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 46.dp, top = 6.dp, start = 44.dp, end = 44.dp)
    )

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        if (uiState.isConnecting) {
            CircularProgressIndicator(
                strokeWidth = 4.dp,
                color = Color.White,
                modifier = Modifier
                    .size(48.dp)
                    .padding(6.dp)
                    .onPreviewKeyEvent { keyEvent ->
                        if (keyEvent.type == KeyEventType.KeyDown) {
                            when (keyEvent.key) {
                                Key.Back -> {
                                    onAction(WelcomeContract.Action.OnInstructionClick(3))
                                    true
                                }
                                Key.DirectionLeft -> {
                                    onAction(WelcomeContract.Action.OnInstructionClick(3))
                                    true
                                }
                                else -> false
                            }
                        } else {
                            false
                        }
                    }
                    .focusable()
            )
        } else {
            Button(
                onClick = { onAction(WelcomeContract.Action.OnInstructionClick(5)) },
                modifier = Modifier
                    .width(200.dp)
                    .height(52.dp)
            ) {
                val (buttonText, buttonFontSize) = responsiveFontSize("Zatwierdź")
                Text(
                    text = buttonText,
                    fontSize = buttonFontSize,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxSize()
                )
            }
        }
    }
}

@Composable
fun InstructionTitle() {
    Text(
        text = buildAnnotatedString {
            withStyle(style = SpanStyle(color = Color.White)) {
                append("Kolejka")
            }
            withStyle(style = SpanStyle(color = Color(0xFFf97316))) {
                append("System")
            }
        },
        fontWeight = FontWeight.Bold,
        textAlign = TextAlign.Center,
        fontSize = 54.sp,
        modifier = Modifier
            .fillMaxWidth()
    )
}

@Preview(
    widthDp = 540,
    heightDp = 960,
    device = Devices.TV_1080p
)
@Composable
fun WelcomeScreenPreview() {
    OrderScreenTheme {
        WelcomeScreenContent(
            uiState = WelcomeContract.State(),
            onAction = {}
        )
    }
}