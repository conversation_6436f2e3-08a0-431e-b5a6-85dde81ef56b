package pl.lazicki.orderscreen.ui.welcome

import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import pl.lazicki.bluetooth.domain.BluetoothController
import pl.lazicki.data.repository.ScreenSettingsRepository
import pl.lazicki.orderscreen.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class WelcomeViewModel @Inject constructor(
    private val screenSettingsRepository: ScreenSettingsRepository,
    private val bluetoothController: BluetoothController,
) : MviViewModel<WelcomeContract.State, WelcomeContract.Action, WelcomeContract.Effect>(
    initialState = WelcomeContract.State(deviceName = screenSettingsRepository.getDeviceName())
) {

    init {
        observeBluetoothConnection()
    }

    override fun onAction(action: WelcomeContract.Action) {
        viewModelScope.launch(Dispatchers.IO) {
            when (action) {
                is WelcomeContract.Action.OnInstructionClick -> {
                    if (action.instructionNumber == 5) {
                        pushEffect(WelcomeContract.Effect.NavigateToOrderScreen)
                    } else {
                        if (action.instructionNumber == 4) {
                            bluetoothController.makeDiscoverable()
                        }
                        updateState { copy(currentInstruction = action.instructionNumber) }
                    }
                }

                WelcomeContract.Action.OnRotateScreen -> {
                    viewModelScope.launch(Dispatchers.IO) {
                        val currentOrientation =
                            screenSettingsRepository.getScreenSettings().screenOrientation
                        val nextOrientation = (currentOrientation + 90) % 360
                        screenSettingsRepository.updateScreenOrientation(nextOrientation)
                        pushEffect(WelcomeContract.Effect.RotateScreen(nextOrientation))
                    }
                }
            }
        }
    }

    private fun observeBluetoothConnection() {
        viewModelScope.launch(Dispatchers.IO) {
            bluetoothController.isConnected.collect { isConnected ->
                if (isConnected) {
                    pushEffect(WelcomeContract.Effect.NavigateToOrderScreen)
                }
                updateState { copy(isConnecting = !isConnected) }
            }
        }
    }
}
