package pl.lazicki.orderscreen.ui.welcome

import dagger.hilt.android.lifecycle.HiltViewModel
import pl.lazicki.data.ScreenSettingsRepository
import pl.lazicki.orderscreen.core.mvi.MviViewModel
import javax.inject.Inject

@HiltViewModel
class WelcomeViewModel @Inject constructor(
    private val screenSettingsRepository: ScreenSettingsRepository,
) : MviViewModel<WelcomeContract.State, WelcomeContract.Action, WelcomeContract.Effect>(
    initialState = WelcomeContract.State(
        currentOrientation = screenSettingsRepository.screenOrientation
    )
) {

    override fun onAction(action: WelcomeContract.Action) {
        when (action) {
            is WelcomeContract.Action.OnInstructionClick -> {
                if (action.instructionNumber == 5) {
                    pushEffect(WelcomeContract.Effect.NavigateToOrderScreen)
                } else {
                    updateState { copy(currentInstruction = action.instructionNumber) }
                }
            }

            WelcomeContract.Action.OnRotateScreen -> {
                val nextOrientation = (currentState.currentOrientation + 90) % 360
                updateState { copy(currentOrientation = nextOrientation) }
                screenSettingsRepository.screenOrientation = nextOrientation
                pushEffect(WelcomeContract.Effect.RotateScreen(nextOrientation))
            }
        }
    }
}
