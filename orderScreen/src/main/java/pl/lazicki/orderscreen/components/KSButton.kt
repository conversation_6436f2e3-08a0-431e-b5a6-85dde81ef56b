package pl.lazicki.orderscreen.components

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.tv.material3.Button
import androidx.tv.material3.ButtonDefaults
import androidx.tv.material3.Text
import pl.lazicki.orderscreen.utils.responsiveFontSize

@Composable
fun KSButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = { onClick() },
        colors = ButtonDefaults.colors(
            containerColor = Color(0xFFF97316), //Color(0xFFB6561A),
            contentColor = Color.White.copy(alpha = 0.9f), //Color(0xFF0f172a),
            focusedContainerColor = Color(0xFFF97316),
            focusedContentColor = Color.White.copy(alpha = 0.9f),
        ),
        shape = ButtonDefaults.shape(shape = RoundedCornerShape(10.dp)),
        modifier = modifier
    ) {
        val (buttonText, buttonFontSize) = responsiveFontSize(text)
        Text(
            text = buttonText,
            fontSize = buttonFontSize,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .fillMaxSize()
        )
    }
}