package pl.lazicki.orderscreen.core

import android.app.ActivityManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import pl.lazicki.orderscreen.ui.main.MainActivity

/**
 * <PERSON><PERSON><PERSON> d<PERSON> w tle, kt<PERSON>ry monitoruje, czy aplikacja jest na pierwszym planie
 * i przywraca ją, jeśli użytkownik próbuje przejść do innej aplikacji.
 */
class KioskService : Service() {
    private val TAG = "KioskService"
    private val serviceScope = CoroutineScope(Dispatchers.Default + Job())
    private var isRunning = false

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Kiosk Service Created")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Kiosk Service Started")
        isRunning = true
        
        // Uruchom monitorowanie w tle
        serviceScope.launch {
            while (isRunning) {
                if (!isAppInForeground()) {
                    Log.d(TAG, "Aplikacja nie jest na pierwszym planie, przywracanie...")
                    startMainActivity()
                }
                delay(1000) // Sprawdzaj co 1000ms
            }
        }
        
        return START_STICKY
    }

    override fun onDestroy() {
        super.onDestroy()
        isRunning = false
        Log.d(TAG, "Kiosk Service Destroyed")
    }

    /**
     * Sprawdza, czy nasza aplikacja jest aktualnie na pierwszym planie
     */
    private fun isAppInForeground(): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val appProcesses = activityManager.runningAppProcesses ?: return false
        val packageName = packageName
        
        for (appProcess in appProcesses) {
            if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND 
                && appProcess.processName == packageName) {
                return true
            }
        }
        return false
    }

    /**
     * Uruchamia MainActivity, aby przywrócić aplikację na pierwszy plan
     */
    private fun startMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
    }

    companion object {
        /**
         * Uruchamia serwis KioskService
         */
        fun start(context: Context) {
            val intent = Intent(context, KioskService::class.java)
            context.startService(intent)
        }

        /**
         * Zatrzymuje serwis KioskService
         */
        fun stop(context: Context) {
            val intent = Intent(context, KioskService::class.java)
            context.stopService(intent)
        }
    }
}
