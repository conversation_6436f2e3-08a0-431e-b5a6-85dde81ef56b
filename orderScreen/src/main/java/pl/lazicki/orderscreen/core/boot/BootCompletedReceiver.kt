package pl.lazicki.orderscreen.core.boot

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import pl.lazicki.orderscreen.core.KioskService
import pl.lazicki.orderscreen.ui.main.MainActivity

class BootCompletedReceiver : BroadcastReceiver() {

    private val TAG = "BootReceiver"

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED) {
            Log.d(TAG, "Boot completed, starting app")

            // Sprawdź, czy tryb kiosk jest aktywny
            if (!isAppRunning(context)) {
                // Uruchom aplikację
                val launchIntent = Intent(context, MainActivity::class.java)
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(launchIntent)

                // Uruchom serwis KioskService
                KioskService.start(context)
            }
        }
    }

    /**
     * Sprawdza, czy aplikacja jest już uruchomiona.
     */
    private fun isAppRunning(context: Context): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val packageName = context.packageName

        val runningAppProcesses = activityManager.runningAppProcesses ?: return false

        for (processInfo in runningAppProcesses) {
            if (processInfo.processName == packageName) {
                return true
            }
        }

        return false
    }
}